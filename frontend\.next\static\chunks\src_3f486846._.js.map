{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,qHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,8CAA8C;YAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,eAAe;gBAAC;gBAAM;gBAAM;aAAK,CAAC,QAAQ,CAAC,cAAc;gBAC3D,UAAU;YACZ;QACF;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;GApCgB", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;;;AAPA;;;;;AAcO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D;GAzBgB;;QACO,mJAAA,CAAA,WAAQ;;;KADf", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAiBO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,qHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,6LAAC,+IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,qHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,6LAAC;oCAAK,WAAU;8CAAW,qHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB;GAnEgB;;QACmB,iIAAA,CAAA,iBAAc;;;KADjC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAhBA;;;;;;;;AAkBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,6LAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,uHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,6LAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB;GA9HgB;;QACqB,kIAAA,CAAA,UAAO;QAC5B,iIAAA,CAAA,iBAAc;;;KAFd", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;AAuBO,SAAS;;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,6LAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,6LAAC;wEAAI,WAAU;;;;;;kFAEjB,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,6LAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,0IAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAjMgB;;QAQV,0IAAA,CAAA,mBAAgB;;;KARN;AAgNT,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,6LAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;MA5BgB;AA+BT,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC;IAbgB;;QACU,0IAAA,CAAA,mBAAgB;;;MAD1B", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const { cartCount, wishlistCount } = useCart()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const { user, profile } = useAuth()\n\n  // تحديد عناصر القائمة بناءً على الحالة\n  let allNavItems: NavItem[]\n\n  if (loading) {\n    // أثناء التحميل، استخدم القائمة الافتراضية\n    allNavItems = defaultNavItems\n  } else if (menuItems.length > 0) {\n    // إذا كانت هناك عناصر من قاعدة البيانات، استخدمها فقط\n    allNavItems = getNavItemsFromDB()\n  } else {\n    // إذا لم تكن هناك عناصر من قاعدة البيانات، استخدم القائمة الافتراضية\n    allNavItems = defaultNavItems\n  }\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartCount > 99 ? '99+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartCount > 9 ? '9+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;;;AA0DO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB;QACtB;+BAAG;QAAC;KAAS;IAIb,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;uDAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,6CAA6C;wBAC7C,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,aAAa,KAAK,SAAS,IAAI,EAAE;wBACnC,OAAO;4BACL,6CAA6C;4BAC7C,QAAQ,IAAI,CAAC;4BACb,aAAa,EAAE;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,mDAAmD;wBACnD,QAAQ,IAAI,CAAC,kDAAkD;wBAC/D,aAAa,EAAE;oBACjB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,uCAAuC;IACvC,IAAI;IAEJ,IAAI,SAAS;QACX,2CAA2C;QAC3C,cAAc;IAChB,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;QAC/B,sDAAsD;QACtD,cAAc;IAChB,OAAO;QACL,qEAAqE;QACrE,cAAc;IAChB;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,6LAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,6LAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,6LAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOlC,6LAAC,8JAAA,CAAA,uBAAoB;;;;;8CAGrB,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;sDACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOhC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,6LAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,6LAAC,yNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,6LAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2IAAA,CAAA,iBAAc;;;;;0DACf,6LAAC,wIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,6LAAC,yIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GAtgBgB;;QACQ,iIAAA,CAAA,iBAAc;QACC,kIAAA,CAAA,UAAO;QAC3B,qIAAA,CAAA,cAAW;QA+JF,kIAAA,CAAA,UAAO;;;KAlKnB;uCAwgBD", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction RadioGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Root>) {\n  return (\n    <RadioGroupPrimitive.Root\n      data-slot=\"radio-group\"\n      className={cn(\"grid gap-3\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction RadioGroupItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Item>) {\n  return (\n    <RadioGroupPrimitive.Item\n      data-slot=\"radio-group-item\"\n      className={cn(\n        \"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator\n        data-slot=\"radio-group-indicator\"\n        className=\"relative flex items-center justify-center\"\n      >\n        <CircleIcon className=\"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n}\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0XACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAC5B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,6MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI9B;MArBS", "debugId": null}}, {"offset": {"line": 2831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 2882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/checkout/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { Navigation } from '@/components/Navigation'\nimport { CheckoutSettingsManager, CheckoutSettings } from '@/lib/checkoutSettings'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Separator } from '@/components/ui/separator'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { \n  CreditCard,\n  MapPin,\n  User,\n  Phone,\n  Mail,\n  Calendar,\n  Shield,\n  Truck,\n  CheckCircle,\n  AlertCircle,\n  ArrowLeft,\n  ArrowRight\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface ShippingAddress {\n  fullName: string\n  phone: string\n  email: string\n  address: string\n  city: string\n  state: string\n  zipCode: string\n  country: string\n}\n\ninterface PaymentMethod {\n  type: 'card' | 'cash' | 'bank_transfer'\n  cardNumber?: string\n  expiryDate?: string\n  cvv?: string\n  cardholderName?: string\n}\n\nexport default function CheckoutPage() {\n  const { user, profile } = useAuth()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({\n    fullName: profile?.full_name || '',\n    phone: profile?.phone || '',\n    email: profile?.email || '',\n    address: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    country: 'الإمارات العربية المتحدة'\n  })\n  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>({\n    type: 'card'\n  })\n  const [deliveryOption, setDeliveryOption] = useState('standard')\n  const [specialInstructions, setSpecialInstructions] = useState('')\n  const [agreeToTerms, setAgreeToTerms] = useState(false)\n  const [loading, setLoading] = useState(false)\n\n  // بيانات الطلب (محاكاة)\n  const orderSummary = {\n    items: [\n      { name: 'زي التخرج الكلاسيكي', quantity: 1, price: 299.99 },\n      { name: 'قبعة التخرج المميزة', quantity: 1, price: 89.99 }\n    ],\n    subtotal: 389.98,\n    discount: 0,\n    shipping: 25,\n    tax: 20.75,\n    total: 435.73\n  }\n\n  const deliveryOptions = [\n    {\n      id: 'standard',\n      name: 'التوصيل العادي',\n      description: '3-5 أيام عمل',\n      price: 25,\n      icon: <Truck className=\"h-5 w-5\" />\n    },\n    {\n      id: 'express',\n      name: 'التوصيل السريع',\n      description: '1-2 أيام عمل',\n      price: 50,\n      icon: <Truck className=\"h-5 w-5\" />\n    },\n    {\n      id: 'same_day',\n      name: 'التوصيل في نفس اليوم',\n      description: 'خلال 6 ساعات',\n      price: 100,\n      icon: <Truck className=\"h-5 w-5\" />\n    }\n  ]\n\n  const paymentMethods = [\n    {\n      id: 'card',\n      name: 'بطاقة ائتمان/خصم',\n      description: 'Visa, Mastercard, American Express',\n      icon: <CreditCard className=\"h-5 w-5\" />\n    },\n    {\n      id: 'cash',\n      name: 'الدفع عند الاستلام',\n      description: 'ادفع نقداً عند وصول الطلب',\n      icon: <CheckCircle className=\"h-5 w-5\" />\n    },\n    {\n      id: 'bank_transfer',\n      name: 'تحويل بنكي',\n      description: 'تحويل مباشر إلى حساب البنك',\n      icon: <Shield className=\"h-5 w-5\" />\n    }\n  ]\n\n  const handleSubmitOrder = async () => {\n    if (!agreeToTerms) {\n      alert('يرجى الموافقة على الشروط والأحكام')\n      return\n    }\n\n    setLoading(true)\n\n    // محاكاة معالجة الطلب\n    setTimeout(() => {\n      setLoading(false)\n\n      // توجيه حسب طريقة الدفع المختارة\n      if (selectedPaymentMethod === 'bank_transfer') {\n        // توجيه إلى صفحة الدفع البنكي\n        const orderId = 'ORD-' + Date.now()\n        const totalAmount = orderSummary.total\n        window.location.href = `/payment/bank-transfer?order_id=${orderId}&amount=${totalAmount}`\n      } else {\n        // توجيه إلى صفحة تأكيد الطلب للطرق الأخرى\n        window.location.href = '/order-confirmation'\n      }\n    }, 2000)\n  }\n\n  const steps = [\n    { id: 1, name: 'معلومات الشحن', icon: <MapPin className=\"h-4 w-4\" /> },\n    { id: 2, name: 'طريقة التوصيل', icon: <Truck className=\"h-4 w-4\" /> },\n    { id: 3, name: 'طريقة الدفع', icon: <CreditCard className=\"h-4 w-4\" /> },\n    { id: 4, name: 'مراجعة الطلب', icon: <CheckCircle className=\"h-4 w-4\" /> }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text\">\n            إتمام الطلب 💳\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n            أكمل معلوماتك لإتمام عملية الشراء\n          </p>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                  currentStep >= step.id\n                    ? 'bg-blue-600 border-blue-600 text-white'\n                    : 'border-gray-300 text-gray-400'\n                }`}>\n                  {currentStep > step.id ? (\n                    <CheckCircle className=\"h-5 w-5\" />\n                  ) : (\n                    step.icon\n                  )}\n                </div>\n                <span className={`ml-3 text-sm font-medium arabic-text ${\n                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'\n                }`}>\n                  {step.name}\n                </span>\n                {index < steps.length - 1 && (\n                  <div className={`w-16 h-0.5 mx-4 ${\n                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Step 1: Shipping Information */}\n            {currentStep === 1 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <MapPin className=\"h-5 w-5\" />\n                    معلومات الشحن\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    أدخل عنوان التوصيل ومعلومات الاتصال\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"fullName\" className=\"arabic-text\">الاسم الكامل</Label>\n                      <Input\n                        id=\"fullName\"\n                        value={shippingAddress.fullName}\n                        onChange={(e) => setShippingAddress(prev => ({\n                          ...prev,\n                          fullName: e.target.value\n                        }))}\n                        className=\"arabic-text\"\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"phone\" className=\"arabic-text\">رقم الهاتف</Label>\n                      <Input\n                        id=\"phone\"\n                        value={shippingAddress.phone}\n                        onChange={(e) => setShippingAddress(prev => ({\n                          ...prev,\n                          phone: e.target.value\n                        }))}\n                        className=\"arabic-text\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"email\" className=\"arabic-text\">البريد الإلكتروني</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={shippingAddress.email}\n                      onChange={(e) => setShippingAddress(prev => ({\n                        ...prev,\n                        email: e.target.value\n                      }))}\n                    />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"address\" className=\"arabic-text\">العنوان التفصيلي</Label>\n                    <Textarea\n                      id=\"address\"\n                      value={shippingAddress.address}\n                      onChange={(e) => setShippingAddress(prev => ({\n                        ...prev,\n                        address: e.target.value\n                      }))}\n                      placeholder=\"رقم المبنى، اسم الشارع، المنطقة...\"\n                      className=\"arabic-text\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-3 gap-4\">\n                    <div>\n                      <Label htmlFor=\"city\" className=\"arabic-text\">المدينة</Label>\n                      <Input\n                        id=\"city\"\n                        value={shippingAddress.city}\n                        onChange={(e) => setShippingAddress(prev => ({\n                          ...prev,\n                          city: e.target.value\n                        }))}\n                        className=\"arabic-text\"\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"state\" className=\"arabic-text\">الإمارة</Label>\n                      <Input\n                        id=\"state\"\n                        value={shippingAddress.state}\n                        onChange={(e) => setShippingAddress(prev => ({\n                          ...prev,\n                          state: e.target.value\n                        }))}\n                        className=\"arabic-text\"\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"zipCode\" className=\"arabic-text\">الرمز البريدي</Label>\n                      <Input\n                        id=\"zipCode\"\n                        value={shippingAddress.zipCode}\n                        onChange={(e) => setShippingAddress(prev => ({\n                          ...prev,\n                          zipCode: e.target.value\n                        }))}\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-end\">\n                    <Button onClick={() => setCurrentStep(2)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 2: Delivery Options */}\n            {currentStep === 2 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <Truck className=\"h-5 w-5\" />\n                    طريقة التوصيل\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    اختر طريقة التوصيل المناسبة لك\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <RadioGroup value={deliveryOption} onValueChange={setDeliveryOption}>\n                    <div className=\"space-y-4\">\n                      {deliveryOptions.map((option) => (\n                        <div key={option.id} className=\"flex items-center space-x-2\">\n                          <RadioGroupItem value={option.id} id={option.id} />\n                          <Label htmlFor={option.id} className=\"flex-1 cursor-pointer\">\n                            <div className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800\">\n                              <div className=\"flex items-center gap-3\">\n                                {option.icon}\n                                <div>\n                                  <p className=\"font-medium arabic-text\">{option.name}</p>\n                                  <p className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">\n                                    {option.description}\n                                  </p>\n                                </div>\n                              </div>\n                              <span className=\"font-bold\">{option.price} درهم</span>\n                            </div>\n                          </Label>\n                        </div>\n                      ))}\n                    </div>\n                  </RadioGroup>\n\n                  <div className=\"mt-6\">\n                    <Label htmlFor=\"instructions\" className=\"arabic-text\">تعليمات خاصة (اختياري)</Label>\n                    <Textarea\n                      id=\"instructions\"\n                      value={specialInstructions}\n                      onChange={(e) => setSpecialInstructions(e.target.value)}\n                      placeholder=\"أي تعليمات خاصة للتوصيل...\"\n                      className=\"arabic-text\"\n                    />\n                  </div>\n\n                  <div className=\"flex justify-between mt-6\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(1)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button onClick={() => setCurrentStep(3)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 3: Payment Method */}\n            {currentStep === 3 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <CreditCard className=\"h-5 w-5\" />\n                    طريقة الدفع\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    اختر طريقة الدفع المفضلة لديك\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <RadioGroup value={paymentMethod.type} onValueChange={(value) => \n                    setPaymentMethod(prev => ({ ...prev, type: value as any }))\n                  }>\n                    <div className=\"space-y-4\">\n                      {paymentMethods.map((method) => (\n                        <div key={method.id} className=\"flex items-center space-x-2\">\n                          <RadioGroupItem value={method.id} id={method.id} />\n                          <Label htmlFor={method.id} className=\"flex-1 cursor-pointer\">\n                            <div className=\"flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800\">\n                              {method.icon}\n                              <div>\n                                <p className=\"font-medium arabic-text\">{method.name}</p>\n                                <p className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">\n                                  {method.description}\n                                </p>\n                              </div>\n                            </div>\n                          </Label>\n                        </div>\n                      ))}\n                    </div>\n                  </RadioGroup>\n\n                  {/* Credit Card Form */}\n                  {paymentMethod.type === 'card' && (\n                    <div className=\"mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800\">\n                      <h3 className=\"font-medium arabic-text\">معلومات البطاقة</h3>\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div className=\"col-span-2\">\n                          <Label htmlFor=\"cardNumber\" className=\"arabic-text\">رقم البطاقة</Label>\n                          <Input\n                            id=\"cardNumber\"\n                            placeholder=\"1234 5678 9012 3456\"\n                            value={paymentMethod.cardNumber || ''}\n                            onChange={(e) => setPaymentMethod(prev => ({\n                              ...prev,\n                              cardNumber: e.target.value\n                            }))}\n                          />\n                        </div>\n                        <div>\n                          <Label htmlFor=\"expiryDate\" className=\"arabic-text\">تاريخ الانتهاء</Label>\n                          <Input\n                            id=\"expiryDate\"\n                            placeholder=\"MM/YY\"\n                            value={paymentMethod.expiryDate || ''}\n                            onChange={(e) => setPaymentMethod(prev => ({\n                              ...prev,\n                              expiryDate: e.target.value\n                            }))}\n                          />\n                        </div>\n                        <div>\n                          <Label htmlFor=\"cvv\" className=\"arabic-text\">CVV</Label>\n                          <Input\n                            id=\"cvv\"\n                            placeholder=\"123\"\n                            value={paymentMethod.cvv || ''}\n                            onChange={(e) => setPaymentMethod(prev => ({\n                              ...prev,\n                              cvv: e.target.value\n                            }))}\n                          />\n                        </div>\n                        <div className=\"col-span-2\">\n                          <Label htmlFor=\"cardholderName\" className=\"arabic-text\">اسم حامل البطاقة</Label>\n                          <Input\n                            id=\"cardholderName\"\n                            value={paymentMethod.cardholderName || ''}\n                            onChange={(e) => setPaymentMethod(prev => ({\n                              ...prev,\n                              cardholderName: e.target.value\n                            }))}\n                            className=\"arabic-text\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  <div className=\"flex justify-between mt-6\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(2)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button onClick={() => setCurrentStep(4)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 4: Order Review */}\n            {currentStep === 4 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <CheckCircle className=\"h-5 w-5\" />\n                    مراجعة الطلب\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    راجع تفاصيل طلبك قبل التأكيد\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  {/* Order Items */}\n                  <div>\n                    <h3 className=\"font-medium mb-3 arabic-text\">المنتجات</h3>\n                    <div className=\"space-y-2\">\n                      {orderSummary.items.map((item, index) => (\n                        <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                          <span className=\"arabic-text\">{item.name} × {item.quantity}</span>\n                          <span>{item.price} درهم</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Shipping Address */}\n                  <div>\n                    <h3 className=\"font-medium mb-3 arabic-text\">عنوان التوصيل</h3>\n                    <div className=\"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                      <p className=\"arabic-text\">{shippingAddress.fullName}</p>\n                      <p className=\"arabic-text\">{shippingAddress.address}</p>\n                      <p className=\"arabic-text\">{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}</p>\n                      <p>{shippingAddress.phone}</p>\n                    </div>\n                  </div>\n\n                  {/* Terms and Conditions */}\n                  <div className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id=\"terms\"\n                      checked={agreeToTerms}\n                      onCheckedChange={setAgreeToTerms}\n                    />\n                    <Label htmlFor=\"terms\" className=\"text-sm arabic-text\">\n                      أوافق على <a href=\"/terms\" className=\"text-blue-600 hover:underline\">الشروط والأحكام</a> و\n                      <a href=\"/privacy\" className=\"text-blue-600 hover:underline\">سياسة الخصوصية</a>\n                    </Label>\n                  </div>\n\n                  <div className=\"flex justify-between\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(3)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button \n                      onClick={handleSubmitOrder} \n                      disabled={!agreeToTerms || loading}\n                      className=\"arabic-text\"\n                    >\n                      {loading ? 'جاري المعالجة...' : 'تأكيد الطلب'}\n                      <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n\n          {/* Order Summary Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <Card className=\"sticky top-24\">\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">ملخص الطلب</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  {orderSummary.items.map((item, index) => (\n                    <div key={index} className=\"flex justify-between text-sm\">\n                      <span className=\"arabic-text\">{item.name} × {item.quantity}</span>\n                      <span>{item.price} درهم</span>\n                    </div>\n                  ))}\n                </div>\n\n                <Separator />\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"arabic-text\">المجموع الفرعي:</span>\n                    <span>{orderSummary.subtotal} درهم</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"arabic-text\">الشحن:</span>\n                    <span>{orderSummary.shipping} درهم</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"arabic-text\">الضريبة:</span>\n                    <span>{orderSummary.tax} درهم</span>\n                  </div>\n                </div>\n\n                <Separator />\n\n                <div className=\"flex justify-between font-bold text-lg\">\n                  <span className=\"arabic-text\">الإجمالي:</span>\n                  <span>{orderSummary.total} درهم</span>\n                </div>\n\n                <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\">\n                  <Shield className=\"h-4 w-4\" />\n                  <span className=\"arabic-text\">دفع آمن ومضمون</span>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;;;;;AAkDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,UAAU,SAAS,aAAa;QAChC,OAAO,SAAS,SAAS;QACzB,OAAO,SAAS,SAAS;QACzB,SAAS;QACT,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,MAAM;IACR;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wBAAwB;IACxB,MAAM,eAAe;QACnB,OAAO;YACL;gBAAE,MAAM;gBAAuB,UAAU;gBAAG,OAAO;YAAO;YAC1D;gBAAE,MAAM;gBAAuB,UAAU;gBAAG,OAAO;YAAM;SAC1D;QACD,UAAU;QACV,UAAU;QACV,UAAU;QACV,KAAK;QACL,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;KACD;IAED,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;YACjB,MAAM;YACN;QACF;QAEA,WAAW;QAEX,sBAAsB;QACtB,WAAW;YACT,WAAW;YAEX,iCAAiC;YACjC,IAAI,0BAA0B,iBAAiB;gBAC7C,8BAA8B;gBAC9B,MAAM,UAAU,SAAS,KAAK,GAAG;gBACjC,MAAM,cAAc,aAAa,KAAK;gBACtC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,gCAAgC,EAAE,QAAQ,QAAQ,EAAE,aAAa;YAC3F,OAAO;gBACL,0CAA0C;gBAC1C,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,GAAG;IACL;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,MAAM;YAAiB,oBAAM,6LAAC,6MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAAa;QACrE;YAAE,IAAI;YAAG,MAAM;YAAiB,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAAa;QACpE;YAAE,IAAI;YAAG,MAAM;YAAe,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAAa;QACvE;YAAE,IAAI;YAAG,MAAM;YAAgB,oBAAM,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAAa;KAC1E;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,6LAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,KAAK,EAAE,GAClB,2CACA,iCACJ;sDACC,cAAc,KAAK,EAAE,iBACpB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;uDAEvB,KAAK,IAAI;;;;;;sDAGb,6LAAC;4CAAK,WAAW,CAAC,qCAAqC,EACrD,eAAe,KAAK,EAAE,GAAG,kBAAkB,iBAC3C;sDACC,KAAK,IAAI;;;;;;wCAEX,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;4CAAI,WAAW,CAAC,gBAAgB,EAC/B,cAAc,KAAK,EAAE,GAAG,gBAAgB,eACxC;;;;;;;mCApBI,KAAK,EAAE;;;;;;;;;;;;;;;kCA2BvB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCAEZ,gBAAgB,mBACf,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGhC,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAW,WAAU;kFAAc;;;;;;kFAClD,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,gBAAgB,QAAQ;wEAC/B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oFAC3C,GAAG,IAAI;oFACP,UAAU,EAAE,MAAM,CAAC,KAAK;gFAC1B,CAAC;wEACD,WAAU;;;;;;;;;;;;0EAGd,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAQ,WAAU;kFAAc;;;;;;kFAC/C,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,gBAAgB,KAAK;wEAC5B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oFAC3C,GAAG,IAAI;oFACP,OAAO,EAAE,MAAM,CAAC,KAAK;gFACvB,CAAC;wEACD,WAAU;;;;;;;;;;;;;;;;;;kEAKhB,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;0EAAc;;;;;;0EAC/C,6LAAC,oIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,gBAAgB,KAAK;gEAC5B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4EAC3C,GAAG,IAAI;4EACP,OAAO,EAAE,MAAM,CAAC,KAAK;wEACvB,CAAC;;;;;;;;;;;;kEAIL,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAc;;;;;;0EACjD,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO,gBAAgB,OAAO;gEAC9B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;4EAC3C,GAAG,IAAI;4EACP,SAAS,EAAE,MAAM,CAAC,KAAK;wEACzB,CAAC;gEACD,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAO,WAAU;kFAAc;;;;;;kFAC9C,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,gBAAgB,IAAI;wEAC3B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oFAC3C,GAAG,IAAI;oFACP,MAAM,EAAE,MAAM,CAAC,KAAK;gFACtB,CAAC;wEACD,WAAU;;;;;;;;;;;;0EAGd,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAQ,WAAU;kFAAc;;;;;;kFAC/C,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,gBAAgB,KAAK;wEAC5B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oFAC3C,GAAG,IAAI;oFACP,OAAO,EAAE,MAAM,CAAC,KAAK;gFACvB,CAAC;wEACD,WAAU;;;;;;;;;;;;0EAGd,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAAc;;;;;;kFACjD,6LAAC,oIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,OAAO,gBAAgB,OAAO;wEAC9B,UAAU,CAAC,IAAM,mBAAmB,CAAA,OAAQ,CAAC;oFAC3C,GAAG,IAAI;oFACP,SAAS,EAAE,MAAM,CAAC,KAAK;gFACzB,CAAC;;;;;;;;;;;;;;;;;;kEAKP,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAS,IAAM,eAAe;4DAAI,WAAU;;gEAAc;8EAEhE,6LAAC,mNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC,6IAAA,CAAA,aAAU;wDAAC,OAAO;wDAAgB,eAAe;kEAChD,cAAA,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;oEAAoB,WAAU;;sFAC7B,6LAAC,6IAAA,CAAA,iBAAc;4EAAC,OAAO,OAAO,EAAE;4EAAE,IAAI,OAAO,EAAE;;;;;;sFAC/C,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,OAAO,EAAE;4EAAE,WAAU;sFACnC,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;4FACZ,OAAO,IAAI;0GACZ,6LAAC;;kHACC,6LAAC;wGAAE,WAAU;kHAA2B,OAAO,IAAI;;;;;;kHACnD,6LAAC;wGAAE,WAAU;kHACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kGAIzB,6LAAC;wFAAK,WAAU;;4FAAa,OAAO,KAAK;4FAAC;;;;;;;;;;;;;;;;;;;mEAbtC,OAAO,EAAE;;;;;;;;;;;;;;;kEAqBzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;0EAAc;;;;;;0EACtD,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;gEACtD,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM,eAAe;gEAAI,WAAU;;oEAAc;kFAEhE,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGpC,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC,6IAAA,CAAA,aAAU;wDAAC,OAAO,cAAc,IAAI;wDAAE,eAAe,CAAC,QACrD,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM;gEAAa,CAAC;kEAEzD,cAAA,6LAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oEAAoB,WAAU;;sFAC7B,6LAAC,6IAAA,CAAA,iBAAc;4EAAC,OAAO,OAAO,EAAE;4EAAE,IAAI,OAAO,EAAE;;;;;;sFAC/C,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,OAAO,EAAE;4EAAE,WAAU;sFACnC,cAAA,6LAAC;gFAAI,WAAU;;oFACZ,OAAO,IAAI;kGACZ,6LAAC;;0GACC,6LAAC;gGAAE,WAAU;0GAA2B,OAAO,IAAI;;;;;;0GACnD,6LAAC;gGAAE,WAAU;0GACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;mEARnB,OAAO,EAAE;;;;;;;;;;;;;;;oDAmBxB,cAAc,IAAI,KAAK,wBACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA0B;;;;;;0EACxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAa,WAAU;0FAAc;;;;;;0FACpD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,aAAY;gFACZ,OAAO,cAAc,UAAU,IAAI;gFACnC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4FACzC,GAAG,IAAI;4FACP,YAAY,EAAE,MAAM,CAAC,KAAK;wFAC5B,CAAC;;;;;;;;;;;;kFAGL,6LAAC;;0FACC,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAa,WAAU;0FAAc;;;;;;0FACpD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,aAAY;gFACZ,OAAO,cAAc,UAAU,IAAI;gFACnC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4FACzC,GAAG,IAAI;4FACP,YAAY,EAAE,MAAM,CAAC,KAAK;wFAC5B,CAAC;;;;;;;;;;;;kFAGL,6LAAC;;0FACC,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAM,WAAU;0FAAc;;;;;;0FAC7C,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,aAAY;gFACZ,OAAO,cAAc,GAAG,IAAI;gFAC5B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4FACzC,GAAG,IAAI;4FACP,KAAK,EAAE,MAAM,CAAC,KAAK;wFACrB,CAAC;;;;;;;;;;;;kFAGL,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAiB,WAAU;0FAAc;;;;;;0FACxD,6LAAC,oIAAA,CAAA,QAAK;gFACJ,IAAG;gFACH,OAAO,cAAc,cAAc,IAAI;gFACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4FACzC,GAAG,IAAI;4FACP,gBAAgB,EAAE,MAAM,CAAC,KAAK;wFAChC,CAAC;gFACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kEAOpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM,eAAe;gEAAI,WAAU;;oEAAc;kFAEhE,6LAAC,mNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGrC,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAI,WAAU;0EACZ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;wEAAgB,WAAU;;0FACzB,6LAAC;gFAAK,WAAU;;oFAAe,KAAK,IAAI;oFAAC;oFAAI,KAAK,QAAQ;;;;;;;0FAC1D,6LAAC;;oFAAM,KAAK,KAAK;oFAAC;;;;;;;;uEAFV;;;;;;;;;;;;;;;;kEAShB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAe,gBAAgB,QAAQ;;;;;;kFACpD,6LAAC;wEAAE,WAAU;kFAAe,gBAAgB,OAAO;;;;;;kFACnD,6LAAC;wEAAE,WAAU;;4EAAe,gBAAgB,IAAI;4EAAC;4EAAG,gBAAgB,KAAK;4EAAC;4EAAE,gBAAgB,OAAO;;;;;;;kFACnG,6LAAC;kFAAG,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;kEAK7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS;gEACT,iBAAiB;;;;;;0EAEnB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;;oEAAsB;kFAC3C,6LAAC;wEAAE,MAAK;wEAAS,WAAU;kFAAgC;;;;;;oEAAmB;kFACxF,6LAAC;wEAAE,MAAK;wEAAW,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAIjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS;gEACT,UAAU,CAAC,gBAAgB;gEAC3B,WAAU;;oEAET,UAAU,qBAAqB;kFAChC,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;;;;;;sDAErC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;8DACZ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAK,WAAU;;wEAAe,KAAK,IAAI;wEAAC;wEAAI,KAAK,QAAQ;;;;;;;8EAC1D,6LAAC;;wEAAM,KAAK,KAAK;wEAAC;;;;;;;;2DAFV;;;;;;;;;;8DAOd,6LAAC,wIAAA,CAAA,YAAS;;;;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,6LAAC;;wEAAM,aAAa,QAAQ;wEAAC;;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,6LAAC;;wEAAM,aAAa,QAAQ;wEAAC;;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,6LAAC;;wEAAM,aAAa,GAAG;wEAAC;;;;;;;;;;;;;;;;;;;8DAI5B,6LAAC,wIAAA,CAAA,YAAS;;;;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,6LAAC;;gEAAM,aAAa,KAAK;gEAAC;;;;;;;;;;;;;8DAG5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GApjBwB;;QACI,kIAAA,CAAA,UAAO;;;KADX", "debugId": null}}]}