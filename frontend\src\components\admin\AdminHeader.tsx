'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu'
import { 
  User, 
  Settings, 
  LogOut, 
  Moon, 
  Sun, 
  ArrowLeft,
  Shield
} from 'lucide-react'
import { useTheme } from 'next-themes'
import Link from 'next/link'

interface AdminHeaderProps {
  title: string
  subtitle?: string
  showBackButton?: boolean
  backUrl?: string
}

export function AdminHeader({ 
  title, 
  subtitle, 
  showBackButton = true, 
  backUrl = '/dashboard/admin' 
}: AdminHeaderProps) {
  const { theme, setTheme } = useTheme()

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* الجانب الأيمن - العنوان والعودة */}
          <div className="flex items-center gap-4">
            {showBackButton && (
              <Link href={backUrl}>
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 ml-2" />
                  العودة لوحة التحكم
                </Button>
              </Link>
            )}
            
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white arabic-text">
                {title}
              </h1>
              {subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                  {subtitle}
                </p>
              )}
            </div>
          </div>

          {/* الجانب الأيسر - أدوات الإدارة */}
          <div className="flex items-center gap-3">
            {/* تبديل الوضع المظلم */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>

            {/* قائمة المستخدم */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-right hidden sm:block">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      مدير النظام
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <EMAIL>
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem>
                  <User className="h-4 w-4 mr-2" />
                  الملف الشخصي
                </DropdownMenuItem>
                
                <DropdownMenuItem>
                  <Settings className="h-4 w-4 mr-2" />
                  إعدادات النظام
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="h-4 w-4 mr-2" />
                  تسجيل الخروج
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* شارة الإدارة */}
            <Badge variant="secondary" className="hidden sm:flex">
              <Shield className="h-3 w-3 mr-1" />
              إدارة
            </Badge>
          </div>
        </div>
      </div>
    </header>
  )
}
