"use client"

import { useState, useEffect } from 'react'

export interface Product {
  id: string
  name: string
  price: string
  originalPrice?: string
  image: string
  rating: number
  reviews: number
  isNew?: boolean
  isFeatured?: boolean
  category: string
  description?: string
  colors?: string[]
  sizes?: string[]
}

interface UseProductsReturn {
  products: Product[]
  featuredProducts: Product[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useProducts(): UseProductsReturn {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'ثوب التخرج الكلاسيكي الأزرق',
      price: '450 Dhs',
      originalPrice: '500 Dhs',
      image: '/products/gown-1.jpg',
      rating: 5,
      reviews: 24,
      isNew: true,
      isFeatured: true,
      category: 'أثواب التخرج',
      description: 'ثوب تخرج أنيق بتصميم كلاسيكي مصنوع من أجود الأقمشة',
      colors: ['أزرق', 'أسود', 'أحمر'],
      sizes: ['S', 'M', 'L', 'XL']
    },
    {
      id: '2',
      name: 'طقم التخرج الفاخر الأسود',
      price: '380 Dhs',
      image: '/products/gown-2.jpg',
      rating: 4,
      reviews: 18,
      isFeatured: true,
      category: 'أطقم كاملة',
      description: 'طقم تخرج فاخر يشمل الثوب والقبعة والشرابة',
      colors: ['أسود', 'أزرق داكن'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL']
    },
    {
      id: '3',
      name: 'ثوب التخرج الأنيق الأحمر',
      price: '420 Dhs',
      image: '/products/gown-3.jpg',
      rating: 5,
      reviews: 31,
      isNew: true,
      category: 'أثواب التخرج',
      description: 'ثوب تخرج بلون أحمر مميز مع تفاصيل ذهبية',
      colors: ['أحمر', 'بورجوندي'],
      sizes: ['S', 'M', 'L', 'XL']
    },
    {
      id: '4',
      name: 'طقم التخرج المميز الذهبي',
      price: '520 Dhs',
      image: '/products/gown-4.jpg',
      rating: 5,
      reviews: 15,
      category: 'أطقم فاخرة',
      description: 'طقم تخرج فاخر بتفاصيل ذهبية للمناسبات الخاصة',
      colors: ['ذهبي', 'فضي'],
      sizes: ['M', 'L', 'XL']
    },
    {
      id: '5',
      name: 'ثوب التخرج العصري الأخضر',
      price: '390 Dhs',
      image: '/products/gown-5.jpg',
      rating: 4,
      reviews: 22,
      isNew: true,
      category: 'أثواب التخرج',
      description: 'ثوب تخرج بتصميم عصري ولون أخضر مميز',
      colors: ['أخضر', 'أخضر داكن'],
      sizes: ['S', 'M', 'L', 'XL']
    },
    {
      id: '6',
      name: 'طقم التخرج الملكي البنفسجي',
      price: '480 Dhs',
      image: '/products/gown-6.jpg',
      rating: 5,
      reviews: 19,
      isFeatured: true,
      category: 'أطقم فاخرة',
      description: 'طقم تخرج ملكي بلون بنفسجي مع تطريز فاخر',
      colors: ['بنفسجي', 'بنفسجي داكن'],
      sizes: ['S', 'M', 'L', 'XL', 'XXL']
    }
  ]

  const fetchProducts = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Mock data - replace with actual API response
      setProducts(mockProducts)
    } catch (err) {
      setError('فشل في تحميل المنتجات')
      console.error('Error fetching products:', err)
    } finally {
      setLoading(false)
    }
  }

  const refetch = () => {
    fetchProducts()
  }

  useEffect(() => {
    fetchProducts()
  }, [])

  const featuredProducts = products.filter(product => product.isFeatured)

  return {
    products,
    featuredProducts,
    loading,
    error,
    refetch
  }
}
