{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/cairo_71c97244.module.css"], "sourcesContent": ["/* arabic */\n@font-face {\n  font-family: 'Cairo';\n  font-style: normal;\n  font-weight: 200 1000;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/cairo/v30/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscQyyS8p4_RHH1.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0897-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC, U+102E0-102FB, U+10E60-10E7E, U+10EC2-10EC4, U+10EFC-10EFF, U+1EE00-1EE03, U+1EE05-1EE1F, U+1EE21-1EE22, U+1EE24, U+1EE27, U+1EE29-1EE32, U+1EE34-1EE37, U+1EE39, U+1EE3B, U+1EE42, U+1EE47, U+1EE49, U+1EE4B, U+1EE4D-1EE4F, U+1EE51-1EE52, U+1EE54, U+1EE57, U+1EE59, U+1EE5B, U+1EE5D, U+1EE5F, U+1EE61-1EE62, U+1EE64, U+1EE67-1EE6A, U+1EE6C-1EE72, U+1EE74-1EE77, U+1EE79-1EE7C, U+1EE7E, U+1EE80-1EE89, U+1EE8B-1EE9B, U+1EEA1-1EEA3, U+1EEA5-1EEA9, U+1EEAB-1EEBB, U+1EEF0-1EEF1;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Cairo';\n  font-style: normal;\n  font-weight: 200 1000;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/cairo/v30/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscSCyS8p4_RHH1.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Cairo';\n  font-style: normal;\n  font-weight: 200 1000;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/cairo/v30/SLXVc1nY6HkvangtZmpQdkhzfH5lkSscRiyS8p4_RA.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Cairo Fallback';\n    src: local(\"Arial\");\n    ascent-override: 137.65%;\ndescent-override: 60.32%;\nline-gap-override: 0.00%;\nsize-adjust: 94.66%;\n\n}\n.className {\n    font-family: 'Cairo', 'Cairo Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-cairo: 'Cairo', 'Cairo Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrMdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrkdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrcdmhHkjko.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 74.67%;\ndescent-override: 21.92%;\nline-gap-override: 0.00%;\nsize-adjust: 134.59%;\n\n}\n.className {\n    font-family: 'Geist Mono', 'Geist Mono Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: var(--font-geist-mono), monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  background-color: hsl(var(--background));\n  font-family: var(--font-cairo), system-ui, sans-serif;\n  color: hsl(var(--foreground));\n}\n\n  /* تحسين عرض النص العربي */\n  html[dir=\"rtl\"] {\n    font-feature-settings: \"liga\" 1, \"kern\" 1;\n  }\n\n  /* تحسين المسافات للنص العربي */\n  .arabic-text {\n    line-height: 1.8;\n    letter-spacing: 0.02em;\n  }\n\n  /* تحسين Grid Layout للـ RTL */\n  html[dir=\"rtl\"] .grid {\n    direction: rtl;\n  }\n\n  /* تحسين Flexbox للـ RTL */\n  html[dir=\"rtl\"] .flex {\n    direction: rtl;\n  }\n\n  /* تحسين عرض البطاقات والمنتجات */\n  html[dir=\"rtl\"] .product-grid,\n  html[dir=\"rtl\"] .category-grid {\n    direction: rtl;\n  }\n\n  /* تحسين عرض الجداول */\n  html[dir=\"rtl\"] table {\n    direction: rtl;\n  }\n\n  /* تحسين عرض الأزرار والعناصر التفاعلية */\n\n  /* تحسين عرض البطاقات في الشبكة */\n  html[dir=\"rtl\"] .product-grid > *,\n  html[dir=\"rtl\"] .category-grid > * {\n    direction: ltr; /* المحتوى الداخلي للبطاقات يبقى LTR */\n  }\n\n  /* تحسين عرض النصوص العربية في البطاقات */\n  html[dir=\"rtl\"] .product-grid .arabic-text,\n  html[dir=\"rtl\"] .category-grid .arabic-text {\n    direction: rtl;\n    text-align: right;\n  }\n\n  /* تحسين عرض الصور والأيقونات */\n  html[dir=\"rtl\"] .product-grid img,\n  html[dir=\"rtl\"] .category-grid img {\n    direction: ltr;\n  }\n\n  /* تحسين عرض الأسعار والأرقام */\n  html[dir=\"rtl\"] .price,\n  html[dir=\"rtl\"] .number {\n    direction: ltr;\n    text-align: left;\n  }\n\n  /* تحسين عرض الجداول في RTL */\n  html[dir=\"rtl\"] table th,\n  html[dir=\"rtl\"] table td {\n    text-align: right;\n  }\n\n  /* تحسين عرض الأرقام والأسعار في الجداول */\n  html[dir=\"rtl\"] table .price,\n  html[dir=\"rtl\"] table .number,\n  html[dir=\"rtl\"] table .rating {\n    text-align: left;\n    direction: ltr;\n  }\n\n  /* تحسين عرض الأزرار في الجداول */\n  html[dir=\"rtl\"] table .actions {\n    direction: ltr;\n  }\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\n@media (min-width: 1400px) {\n\n  .container {\n    max-width: 1400px;\n  }\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.visible {\n  visibility: visible;\n}\n.invisible {\n  visibility: hidden;\n}\n.static {\n  position: static;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.sticky {\n  position: sticky;\n}\n.-inset-1 {\n  inset: -0.25rem;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\n.-bottom-1 {\n  bottom: -0.25rem;\n}\n.-bottom-2 {\n  bottom: -0.5rem;\n}\n.-left-6 {\n  left: -1.5rem;\n}\n.-right-1 {\n  right: -0.25rem;\n}\n.-right-2 {\n  right: -0.5rem;\n}\n.-right-6 {\n  right: -1.5rem;\n}\n.-top-1 {\n  top: -0.25rem;\n}\n.-top-2 {\n  top: -0.5rem;\n}\n.-top-4 {\n  top: -1rem;\n}\n.-top-8 {\n  top: -2rem;\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-2 {\n  bottom: 0.5rem;\n}\n.bottom-20 {\n  bottom: 5rem;\n}\n.bottom-32 {\n  bottom: 8rem;\n}\n.bottom-4 {\n  bottom: 1rem;\n}\n.bottom-40 {\n  bottom: 10rem;\n}\n.bottom-6 {\n  bottom: 1.5rem;\n}\n.bottom-8 {\n  bottom: 2rem;\n}\n.left-0 {\n  left: 0px;\n}\n.left-1 {\n  left: 0.25rem;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-10 {\n  left: 2.5rem;\n}\n.left-2 {\n  left: 0.5rem;\n}\n.left-20 {\n  left: 5rem;\n}\n.left-3 {\n  left: 0.75rem;\n}\n.left-32 {\n  left: 8rem;\n}\n.left-4 {\n  left: 1rem;\n}\n.left-6 {\n  left: 1.5rem;\n}\n.left-8 {\n  left: 2rem;\n}\n.left-\\[50\\%\\] {\n  left: 50%;\n}\n.right-0 {\n  right: 0px;\n}\n.right-10 {\n  right: 2.5rem;\n}\n.right-2 {\n  right: 0.5rem;\n}\n.right-20 {\n  right: 5rem;\n}\n.right-3 {\n  right: 0.75rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.right-6 {\n  right: 1.5rem;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1 {\n  top: 0.25rem;\n}\n.top-1\\/2 {\n  top: 50%;\n}\n.top-10 {\n  top: 2.5rem;\n}\n.top-12 {\n  top: 3rem;\n}\n.top-16 {\n  top: 4rem;\n}\n.top-2 {\n  top: 0.5rem;\n}\n.top-20 {\n  top: 5rem;\n}\n.top-24 {\n  top: 6rem;\n}\n.top-3 {\n  top: 0.75rem;\n}\n.top-32 {\n  top: 8rem;\n}\n.top-4 {\n  top: 1rem;\n}\n.top-40 {\n  top: 10rem;\n}\n.top-8 {\n  top: 2rem;\n}\n.top-\\[1px\\] {\n  top: 1px;\n}\n.top-\\[50\\%\\] {\n  top: 50%;\n}\n.top-\\[60\\%\\] {\n  top: 60%;\n}\n.top-full {\n  top: 100%;\n}\n.isolate {\n  isolation: isolate;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.z-\\[1\\] {\n  z-index: 1;\n}\n.col-span-2 {\n  grid-column: span 2 / span 2;\n}\n.col-start-2 {\n  grid-column-start: 2;\n}\n.row-span-2 {\n  grid-row: span 2 / span 2;\n}\n.row-start-1 {\n  grid-row-start: 1;\n}\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\n.mx-1 {\n  margin-left: 0.25rem;\n  margin-right: 0.25rem;\n}\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.my-8 {\n  margin-top: 2rem;\n  margin-bottom: 2rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-12 {\n  margin-bottom: 3rem;\n}\n.mb-16 {\n  margin-bottom: 4rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.ml-3 {\n  margin-left: 0.75rem;\n}\n.ml-4 {\n  margin-left: 1rem;\n}\n.ml-6 {\n  margin-left: 1.5rem;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.mr-1 {\n  margin-right: 0.25rem;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mr-4 {\n  margin-right: 1rem;\n}\n.mr-6 {\n  margin-right: 1.5rem;\n}\n.mr-8 {\n  margin-right: 2rem;\n}\n.mr-auto {\n  margin-right: auto;\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: 0.25rem;\n}\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\n.mt-12 {\n  margin-top: 3rem;\n}\n.mt-16 {\n  margin-top: 4rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-3 {\n  margin-top: 0.75rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.mt-6 {\n  margin-top: 1.5rem;\n}\n.mt-8 {\n  margin-top: 2rem;\n}\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.aspect-\\[4\\/5\\] {\n  aspect-ratio: 4/5;\n}\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\n.size-2 {\n  width: 0.5rem;\n  height: 0.5rem;\n}\n.size-3 {\n  width: 0.75rem;\n  height: 0.75rem;\n}\n.size-3\\.5 {\n  width: 0.875rem;\n  height: 0.875rem;\n}\n.size-4 {\n  width: 1rem;\n  height: 1rem;\n}\n.size-8 {\n  width: 2rem;\n  height: 2rem;\n}\n.size-9 {\n  width: 2.25rem;\n  height: 2.25rem;\n}\n.size-full {\n  width: 100%;\n  height: 100%;\n}\n.h-0\\.5 {\n  height: 0.125rem;\n}\n.h-1 {\n  height: 0.25rem;\n}\n.h-1\\.5 {\n  height: 0.375rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-14 {\n  height: 3.5rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-2\\.5 {\n  height: 0.625rem;\n}\n.h-20 {\n  height: 5rem;\n}\n.h-24 {\n  height: 6rem;\n}\n.h-3 {\n  height: 0.75rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-40 {\n  height: 10rem;\n}\n.h-48 {\n  height: 12rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-64 {\n  height: 16rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-80 {\n  height: 20rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-96 {\n  height: 24rem;\n}\n.h-\\[1\\.15rem\\] {\n  height: 1.15rem;\n}\n.h-\\[1\\.2rem\\] {\n  height: 1.2rem;\n}\n.h-\\[calc\\(100\\%-1px\\)\\] {\n  height: calc(100% - 1px);\n}\n.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n  height: var(--radix-navigation-menu-viewport-height);\n}\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\n.h-full {\n  height: 100%;\n}\n.h-px {\n  height: 1px;\n}\n.h-screen {\n  height: 100vh;\n}\n.max-h-0 {\n  max-height: 0px;\n}\n.max-h-48 {\n  max-height: 12rem;\n}\n.max-h-60 {\n  max-height: 15rem;\n}\n.max-h-\\[80vh\\] {\n  max-height: 80vh;\n}\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\n.max-h-screen {\n  max-height: 100vh;\n}\n.min-h-16 {\n  min-height: 4rem;\n}\n.min-h-4 {\n  min-height: 1rem;\n}\n.min-h-\\[100px\\] {\n  min-height: 100px;\n}\n.min-h-\\[60vh\\] {\n  min-height: 60vh;\n}\n.min-h-\\[calc\\(100vh-200px\\)\\] {\n  min-height: calc(100vh - 200px);\n}\n.min-h-\\[calc\\(100vh-80px\\)\\] {\n  min-height: calc(100vh - 80px);\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-0\\.5 {\n  width: 0.125rem;\n}\n.w-1 {\n  width: 0.25rem;\n}\n.w-1\\.5 {\n  width: 0.375rem;\n}\n.w-1\\/2 {\n  width: 50%;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-14 {\n  width: 3.5rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-2\\.5 {\n  width: 0.625rem;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-24 {\n  width: 6rem;\n}\n.w-3 {\n  width: 0.75rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-4\\/6 {\n  width: 66.666667%;\n}\n.w-40 {\n  width: 10rem;\n}\n.w-48 {\n  width: 12rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-5\\/6 {\n  width: 83.333333%;\n}\n.w-56 {\n  width: 14rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-64 {\n  width: 16rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-80 {\n  width: 20rem;\n}\n.w-9 {\n  width: 2.25rem;\n}\n.w-\\[1\\.2rem\\] {\n  width: 1.2rem;\n}\n.w-fit {\n  width: -moz-fit-content;\n  width: fit-content;\n}\n.w-full {\n  width: 100%;\n}\n.w-max {\n  width: -moz-max-content;\n  width: max-content;\n}\n.w-px {\n  width: 1px;\n}\n.min-w-0 {\n  min-width: 0px;\n}\n.min-w-\\[140px\\] {\n  min-width: 140px;\n}\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-5xl {\n  max-width: 64rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-7xl {\n  max-width: 80rem;\n}\n.max-w-\\[80\\%\\] {\n  max-width: 80%;\n}\n.max-w-\\[calc\\(100\\%-2rem\\)\\] {\n  max-width: calc(100% - 2rem);\n}\n.max-w-lg {\n  max-width: -moz-max-content;\n  max-width: max-content;\n}\n.max-w-max {\n  max-width: -moz-max-content;\n  max-width: max-content;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-none {\n  max-width: none;\n}\n.max-w-sm {\n  max-width: 24rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.grow {\n  flex-grow: 1;\n}\n.caption-bottom {\n  caption-side: bottom;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\.5 {\n  --tw-translate-y: -0.375rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-0 {\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-1\\.5 {\n  --tw-translate-y: 0.375rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-10 {\n  --tw-translate-y: 2.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-rotate-12 {\n  --tw-rotate: -12deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-rotate-45 {\n  --tw-rotate: -45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-12 {\n  --tw-rotate: 12deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-0 {\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes bounce {\n\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\n.animate-bounce {\n  animation: bounce 1s infinite;\n}\n@keyframes ping {\n\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n.animate-ping {\n  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n.cursor-default {\n  cursor: default;\n}\n.cursor-grab {\n  cursor: grab;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.touch-none {\n  touch-action: none;\n}\n.select-none {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.scroll-my-1 {\n  scroll-margin-top: 0.25rem;\n  scroll-margin-bottom: 0.25rem;\n}\n.list-none {\n  list-style-type: none;\n}\n.auto-rows-min {\n  grid-auto-rows: min-content;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n.grid-cols-5 {\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\n.grid-cols-\\[0_1fr\\] {\n  grid-template-columns: 0 1fr;\n}\n.grid-rows-\\[auto_auto\\] {\n  grid-template-rows: auto auto;\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-items-start {\n  justify-items: start;\n}\n.gap-0\\.5 {\n  gap: 0.125rem;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.gap-y-0\\.5 {\n  row-gap: 0.125rem;\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-0 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\n}\n.space-y-0\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.space-x-reverse > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 1;\n}\n.divide-y > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\n.self-start {\n  align-self: flex-start;\n}\n.justify-self-end {\n  justify-self: end;\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.break-all {\n  word-break: break-all;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-2xl {\n  border-radius: 1rem;\n}\n.rounded-\\[4px\\] {\n  border-radius: 4px;\n}\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-none {\n  border-radius: 0px;\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.rounded-l-lg {\n  border-top-left-radius: var(--radius);\n  border-bottom-left-radius: var(--radius);\n}\n.rounded-r-lg {\n  border-top-right-radius: var(--radius);\n  border-bottom-right-radius: var(--radius);\n}\n.rounded-t-full {\n  border-top-left-radius: 9999px;\n  border-top-right-radius: 9999px;\n}\n.rounded-t-lg {\n  border-top-left-radius: var(--radius);\n  border-top-right-radius: var(--radius);\n}\n.rounded-tl-lg {\n  border-top-left-radius: var(--radius);\n}\n.rounded-tl-sm {\n  border-top-left-radius: calc(var(--radius) - 4px);\n}\n.rounded-tr-lg {\n  border-top-right-radius: var(--radius);\n}\n.border {\n  border-width: 1px;\n}\n.border-0 {\n  border-width: 0px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-4 {\n  border-width: 4px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-b-2 {\n  border-bottom-width: 2px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-l-4 {\n  border-left-width: 4px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-r-4 {\n  border-right-width: 4px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-dashed {\n  border-style: dashed;\n}\n.border-blue-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-blue-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-gray-800 {\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\n.border-gray-900 {\n  --tw-border-opacity: 1;\n  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.border-green-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-green-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\n.border-green-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.border-indigo-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-orange-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\n.border-orange-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-purple-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\n}\n.border-purple-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.border-purple-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.border-red-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-red-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.border-red-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.border-rose-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(225 29 72 / var(--tw-border-opacity, 1));\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-yellow-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\n.border-yellow-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));\n}\n.border-l-transparent {\n  border-left-color: transparent;\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.bg-black\\/20 {\n  background-color: rgb(0 0 0 / 0.2);\n}\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/60 {\n  background-color: rgb(0 0 0 / 0.6);\n}\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\n.bg-blue-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50\\/30 {\n  background-color: rgb(239 246 255 / 0.3);\n}\n.bg-blue-50\\/50 {\n  background-color: rgb(239 246 255 / 0.5);\n}\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600\\/20 {\n  background-color: rgb(37 99 235 / 0.2);\n}\n.bg-blue-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\n.bg-border {\n  background-color: hsl(var(--border));\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-current {\n  background-color: currentColor;\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\n}\n.bg-green-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\n}\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\n.bg-muted\\/30 {\n  background-color: hsl(var(--muted) / 0.3);\n}\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\n.bg-orange-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\n}\n.bg-pink-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));\n}\n.bg-pink-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));\n}\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\n.bg-purple-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-red-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-red-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-sky-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\n}\n.bg-teal-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/20 {\n  background-color: rgb(255 255 255 / 0.2);\n}\n.bg-white\\/30 {\n  background-color: rgb(255 255 255 / 0.3);\n}\n.bg-white\\/80 {\n  background-color: rgb(255 255 255 / 0.8);\n}\n.bg-white\\/90 {\n  background-color: rgb(255 255 255 / 0.9);\n}\n.bg-white\\/95 {\n  background-color: rgb(255 255 255 / 0.95);\n}\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-t {\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\n.from-blue-100 {\n  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500 {\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500\\/20 {\n  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-600 {\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-100 {\n  --tw-gradient-from: #f3f4f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(243 244 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-50 {\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-50 {\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-500 {\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-50 {\n  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-indigo-500 {\n  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-50 {\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-100 {\n  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-50 {\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500 {\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-50 {\n  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-500 {\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-rose-100 {\n  --tw-gradient-from: #ffe4e6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 228 230 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-rose-50 {\n  --tw-gradient-from: #fff1f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 241 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white\\/20 {\n  --tw-gradient-from: rgb(255 255 255 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-200\\/50 {\n  --tw-gradient-from: rgb(254 240 138 / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 240 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-400 {\n  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-500 {\n  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-purple-600 {\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-blue-50 {\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\n}\n.to-blue-600 {\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\n}\n.to-blue-800 {\n  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);\n}\n.to-cyan-50 {\n  --tw-gradient-to: #ecfeff var(--tw-gradient-to-position);\n}\n.to-gray-200 {\n  --tw-gradient-to: #e5e7eb var(--tw-gradient-to-position);\n}\n.to-green-600 {\n  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);\n}\n.to-indigo-600 {\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\n.to-orange-100 {\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\n}\n.to-orange-50 {\n  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);\n}\n.to-pink-100 {\n  --tw-gradient-to: #fce7f3 var(--tw-gradient-to-position);\n}\n.to-pink-50 {\n  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);\n}\n.to-purple-100 {\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\n}\n.to-purple-50 {\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\n}\n.to-purple-500 {\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\n}\n.to-purple-500\\/20 {\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\n}\n.to-purple-600 {\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-red-50 {\n  --tw-gradient-to: #fef2f2 var(--tw-gradient-to-position);\n}\n.to-red-600 {\n  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);\n}\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-white {\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\n}\n.to-yellow-50 {\n  --tw-gradient-to: #fefce8 var(--tw-gradient-to-position);\n}\n.to-yellow-600 {\n  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);\n}\n.bg-clip-text {\n  -webkit-background-clip: text;\n          background-clip: text;\n}\n.fill-current {\n  fill: currentColor;\n}\n.fill-primary {\n  fill: hsl(var(--primary));\n}\n.fill-red-500 {\n  fill: #ef4444;\n}\n.fill-yellow-400 {\n  fill: #facc15;\n}\n.object-cover {\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.p-0 {\n  padding: 0px;\n}\n.p-1 {\n  padding: 0.25rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.p-\\[3px\\] {\n  padding: 3px;\n}\n.p-px {\n  padding: 1px;\n}\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0 {\n  padding-top: 0px;\n  padding-bottom: 0px;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\n.pb-4 {\n  padding-bottom: 1rem;\n}\n.pb-6 {\n  padding-bottom: 1.5rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-2 {\n  padding-left: 0.5rem;\n}\n.pl-8 {\n  padding-left: 2rem;\n}\n.pr-10 {\n  padding-right: 2.5rem;\n}\n.pr-2 {\n  padding-right: 0.5rem;\n}\n.pr-2\\.5 {\n  padding-right: 0.625rem;\n}\n.pr-4 {\n  padding-right: 1rem;\n}\n.pr-8 {\n  padding-right: 2rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.pt-2 {\n  padding-top: 0.5rem;\n}\n.pt-3 {\n  padding-top: 0.75rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.pt-6 {\n  padding-top: 1.5rem;\n}\n.pt-8 {\n  padding-top: 2rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.text-right {\n  text-align: right;\n}\n.align-middle {\n  vertical-align: middle;\n}\n.font-cairo {\n  font-family: var(--font-cairo), system-ui, sans-serif;\n}\n.font-mono {\n  font-family: var(--font-geist-mono), monospace;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-6xl {\n  font-size: 3.75rem;\n  line-height: 1;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-normal {\n  font-weight: 400;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.leading-none {\n  line-height: 1;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.leading-tight {\n  line-height: 1.25;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\n.text-amber-500 {\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\n.text-blue-100 {\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n.text-blue-200 {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n.text-blue-400 {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-blue-900 {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-current {\n  color: currentColor;\n}\n.text-cyan-600 {\n  --tw-text-opacity: 1;\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\n}\n.text-destructive {\n  color: hsl(var(--destructive));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.text-green-800 {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.text-green-900 {\n  --tw-text-opacity: 1;\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\n}\n.text-indigo-800 {\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-orange-500 {\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\n.text-orange-600 {\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\n.text-orange-800 {\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\n.text-pink-800 {\n  --tw-text-opacity: 1;\n  color: rgb(157 23 77 / var(--tw-text-opacity, 1));\n}\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-purple-500 {\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.text-purple-600 {\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-purple-800 {\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700 {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800 {\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-red-900 {\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-teal-600 {\n  --tw-text-opacity: 1;\n  color: rgb(13 148 136 / var(--tw-text-opacity, 1));\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-300 {\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\n.text-yellow-400 {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.text-yellow-700 {\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.text-yellow-900 {\n  --tw-text-opacity: 1;\n  color: rgb(113 63 18 / var(--tw-text-opacity, 1));\n}\n.line-through {\n  text-decoration-line: line-through;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-10 {\n  opacity: 0.1;\n}\n.opacity-100 {\n  opacity: 1;\n}\n.opacity-20 {\n  opacity: 0.2;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-60 {\n  opacity: 0.6;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.opacity-75 {\n  opacity: 0.75;\n}\n.opacity-80 {\n  opacity: 0.8;\n}\n.opacity-90 {\n  opacity: 0.9;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-blue-600\\/25 {\n  --tw-shadow-color: rgb(37 99 235 / 0.25);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.outline {\n  outline-style: solid;\n}\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-blue-200 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));\n}\n.ring-blue-500 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.ring-primary {\n  --tw-ring-color: hsl(var(--primary));\n}\n.ring-ring\\/50 {\n  --tw-ring-color: hsl(var(--ring) / 0.5);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-lg {\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[color\\2c box-shadow\\] {\n  transition-property: color,box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-none {\n  transition-property: none;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.delay-1000 {\n  transition-delay: 1000ms;\n}\n.delay-200 {\n  transition-delay: 200ms;\n}\n.delay-300 {\n  transition-delay: 300ms;\n}\n.delay-500 {\n  transition-delay: 500ms;\n}\n.duration-1000 {\n  transition-duration: 1000ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.duration-500 {\n  transition-duration: 500ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\n.slide-in-from-right {\n  --tw-enter-translate-x: 100%;\n}\n.slide-in-from-right-full {\n  --tw-enter-translate-x: 100%;\n}\n.duration-1000 {\n  animation-duration: 1000ms;\n}\n.duration-200 {\n  animation-duration: 200ms;\n}\n.duration-300 {\n  animation-duration: 300ms;\n}\n.duration-500 {\n  animation-duration: 500ms;\n}\n.delay-1000 {\n  animation-delay: 1000ms;\n}\n.delay-200 {\n  animation-delay: 200ms;\n}\n.delay-300 {\n  animation-delay: 300ms;\n}\n.delay-500 {\n  animation-delay: 500ms;\n}\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n/* Smooth scrolling */\nhtml {\n    scroll-behavior: smooth;\n  }\n/* Floating animation */\n@keyframes float {\n    0%, 100% {\n      transform: translateY(0px);\n    }\n    50% {\n      transform: translateY(-20px);\n    }\n  }\n/* Pulse glow effect */\n@keyframes pulse-glow {\n    0%, 100% {\n      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);\n    }\n    50% {\n      box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);\n    }\n  }\n/* Slide in from bottom */\n@keyframes slide-in-bottom {\n    from {\n      opacity: 0;\n      transform: translateY(50px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n/* Fade in scale */\n@keyframes fade-in-scale {\n    from {\n      opacity: 0;\n      transform: scale(0.9);\n    }\n    to {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n/* Shimmer effect */\n@keyframes shimmer {\n    0% {\n      background-position: -200% 0;\n    }\n    100% {\n      background-position: 200% 0;\n    }\n  }\n/* Hover lift effect */\n/* Backdrop blur enhanced */\n/* Gradient border animation */\n@keyframes gradient-border {\n    0%, 100% {\n      background-position: 0% 50%;\n    }\n    50% {\n      background-position: 100% 50%;\n    }\n  }\n/* Smooth transitions */\n/* Loading spinner */\n/* Line clamp utilities */\n.line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n\n:root {\n  --background: 0 0% 100%;\n  --foreground: 222.2 84% 4.9%;\n  --card: 0 0% 100%;\n  --card-foreground: 222.2 84% 4.9%;\n  --popover: 0 0% 100%;\n  --popover-foreground: 222.2 84% 4.9%;\n  --primary: 222.2 47.4% 11.2%;\n  --primary-foreground: 210 40% 98%;\n  --secondary: 210 40% 96%;\n  --secondary-foreground: 222.2 47.4% 11.2%;\n  --muted: 210 40% 96%;\n  --muted-foreground: 215.4 16.3% 46.9%;\n  --accent: 210 40% 96%;\n  --accent-foreground: 222.2 47.4% 11.2%;\n  --destructive: 0 84.2% 60.2%;\n  --destructive-foreground: 210 40% 98%;\n  --border: 214.3 31.8% 91.4%;\n  --input: 214.3 31.8% 91.4%;\n  --ring: 222.2 84% 4.9%;\n  --radius: 0.5rem;\n}\n\n.dark {\n  --background: 222.2 84% 4.9%;\n  --foreground: 210 40% 98%;\n  --card: 222.2 84% 4.9%;\n  --card-foreground: 210 40% 98%;\n  --popover: 222.2 84% 4.9%;\n  --popover-foreground: 210 40% 98%;\n  --primary: 210 40% 98%;\n  --primary-foreground: 222.2 47.4% 11.2%;\n  --secondary: 217.2 32.6% 17.5%;\n  --secondary-foreground: 210 40% 98%;\n  --muted: 217.2 32.6% 17.5%;\n  --muted-foreground: 215 20.2% 65.1%;\n  --accent: 217.2 32.6% 17.5%;\n  --accent-foreground: 210 40% 98%;\n  --destructive: 0 62.8% 30.6%;\n  --destructive-foreground: 210 40% 98%;\n  --border: 217.2 32.6% 17.5%;\n  --input: 217.2 32.6% 17.5%;\n  --ring: 212.7 26.8% 83.9%;\n}\n\n/* Navigation animations */\n@keyframes slideInFromRight {\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes pulse-scale {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n/* Smooth transitions for RTL/LTR switching */\nhtml {\n  transition: direction 0.3s ease;\n}\n\n/* Enhanced focus styles for accessibility */\n.focus-visible:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #888;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #555;\n}\n\n/* Dark mode scrollbar */\n.dark ::-webkit-scrollbar-track {\n  background: #374151;\n}\n\n.dark ::-webkit-scrollbar-thumb {\n  background: #6b7280;\n}\n\n.dark ::-webkit-scrollbar-thumb:hover {\n  background: #9ca3af;\n}\n\n/* تنسيق العملة RTL - العملة من الجهة اليمنى */\n.currency-rtl {\n  direction: ltr;\n  text-align: right;\n  unicode-bidi: bidi-override;\n}\n\n.currency-rtl::after {\n  content: \" Dhs\";\n  margin-left: 0.25rem;\n}\n\n/* فئة للأسعار مع تنسيق RTL */\n.price-rtl {\n  direction: ltr;\n  text-align: right;\n  display: inline-block;\n}\n\n.price-rtl .currency {\n  margin-left: 0.25rem;\n}\n\n/* تحسين عرض الأرقام والعملة في البيئة العربية */\nhtml[dir=\"rtl\"] .price,\nhtml[dir=\"rtl\"] .currency,\nhtml[dir=\"rtl\"] .amount {\n  direction: ltr;\n  text-align: right;\n  display: inline-block;\n}\n\n/* تنسيق خاص للجداول */\nhtml[dir=\"rtl\"] table .price,\nhtml[dir=\"rtl\"] table .currency,\nhtml[dir=\"rtl\"] table .amount {\n  text-align: left;\n}\n\n/* تنسيق للبطاقات والكروت */\nhtml[dir=\"rtl\"] .card .price,\nhtml[dir=\"rtl\"] .card .currency,\nhtml[dir=\"rtl\"] .card .amount {\n  text-align: right;\n}\n\n/* Enhanced Animations and Effects for Homepage */\nhtml[dir=\"rtl\"] .\\*\\:data-\\[slot\\=select-value\\]\\:flex[data-slot=\"select-value\"] > * {\n    direction: rtl;\n  }\n@media (min-width: 640px) {\n  html[dir=\"rtl\"] .sm\\:flex {\n    direction: rtl;\n  }\n}\n@media (min-width: 768px) {\n  html[dir=\"rtl\"] .md\\:flex {\n    direction: rtl;\n  }\n}\n@media (min-width: 1024px) {\n  html[dir=\"rtl\"] .lg\\:flex {\n    direction: rtl;\n  }\n}\n.selection\\:bg-primary *::-moz-selection {\n  background-color: hsl(var(--primary));\n}\n.selection\\:bg-primary *::selection {\n  background-color: hsl(var(--primary));\n}\n.selection\\:text-primary-foreground *::-moz-selection {\n  color: hsl(var(--primary-foreground));\n}\n.selection\\:text-primary-foreground *::selection {\n  color: hsl(var(--primary-foreground));\n}\n.selection\\:bg-primary::-moz-selection {\n  background-color: hsl(var(--primary));\n}\n.selection\\:bg-primary::selection {\n  background-color: hsl(var(--primary));\n}\n.selection\\:text-primary-foreground::-moz-selection {\n  color: hsl(var(--primary-foreground));\n}\n.selection\\:text-primary-foreground::selection {\n  color: hsl(var(--primary-foreground));\n}\n.file\\:inline-flex::file-selector-button {\n  display: inline-flex;\n}\n.file\\:h-7::file-selector-button {\n  height: 1.75rem;\n}\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\n.placeholder\\:text-muted-foreground::-moz-placeholder {\n  color: hsl(var(--muted-foreground));\n}\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n.first\\:rounded-l-lg:first-child {\n  border-top-left-radius: var(--radius);\n  border-bottom-left-radius: var(--radius);\n}\n.last\\:rounded-r-lg:last-child {\n  border-top-right-radius: var(--radius);\n  border-bottom-right-radius: var(--radius);\n}\n.hover\\:-translate-y-2:hover {\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-110:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:border-blue-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-blue-500:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n.hover\\:bg-black\\/10:hover {\n  background-color: rgb(0 0 0 / 0.1);\n}\n.hover\\:bg-blue-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\n.hover\\:bg-pink-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(190 24 93 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n.hover\\:bg-red-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n.hover\\:bg-sky-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-white:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-white\\/20:hover {\n  background-color: rgb(255 255 255 / 0.2);\n}\n.hover\\:from-blue-700:hover {\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:to-purple-700:hover {\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n.hover\\:text-blue-400:hover {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-green-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n.hover\\:opacity-80:hover {\n  opacity: 0.8;\n}\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-sm:hover {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:ring-4:hover {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\n.focus\\:text-red-600:focus {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n.focus-visible\\:border-ring:focus-visible {\n  border-color: hsl(var(--ring));\n}\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus-visible\\:outline-1:focus-visible {\n  outline-width: 1px;\n}\n.focus-visible\\:outline-ring:focus-visible {\n  outline-color: hsl(var(--ring));\n}\n.focus-visible\\:ring-1:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-4:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-\\[3px\\]:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-destructive\\/20:focus-visible {\n  --tw-ring-color: hsl(var(--destructive) / 0.2);\n}\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus-visible\\:ring-ring\\/50:focus-visible {\n  --tw-ring-color: hsl(var(--ring) / 0.5);\n}\n.active\\:cursor-grabbing:active {\n  cursor: grabbing;\n}\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n.group:hover .group-hover\\:visible {\n  visibility: visible;\n}\n.group\\/btn:hover .group-hover\\/btn\\:translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-orange-600 {\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-purple-600 {\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-teal-600 {\n  --tw-text-opacity: 1;\n  color: rgb(13 148 136 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\n.peer:disabled ~ .peer-disabled\\:opacity-50 {\n  opacity: 0.5;\n}\n.has-\\[\\>svg\\]\\:grid-cols-\\[calc\\(var\\(--spacing\\)\\*4\\)_1fr\\]:has(>svg) {\n  grid-template-columns: calc(var(--spacing) * 4) 1fr;\n}\n.has-\\[\\>svg\\]\\:gap-x-3:has(>svg) {\n  -moz-column-gap: 0.75rem;\n       column-gap: 0.75rem;\n}\n.has-\\[\\>svg\\]\\:px-2\\.5:has(>svg) {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.has-\\[\\>svg\\]\\:px-3:has(>svg) {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.has-\\[\\>svg\\]\\:px-4:has(>svg) {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\n.data-\\[orientation\\=horizontal\\]\\:h-1\\.5[data-orientation=\"horizontal\"] {\n  height: 0.375rem;\n}\n.data-\\[orientation\\=horizontal\\]\\:h-full[data-orientation=\"horizontal\"] {\n  height: 100%;\n}\n.data-\\[orientation\\=horizontal\\]\\:h-px[data-orientation=\"horizontal\"] {\n  height: 1px;\n}\n.data-\\[orientation\\=vertical\\]\\:h-full[data-orientation=\"vertical\"] {\n  height: 100%;\n}\n.data-\\[size\\=default\\]\\:h-9[data-size=\"default\"] {\n  height: 2.25rem;\n}\n.data-\\[size\\=sm\\]\\:h-8[data-size=\"sm\"] {\n  height: 2rem;\n}\n.data-\\[orientation\\=vertical\\]\\:min-h-44[data-orientation=\"vertical\"] {\n  min-height: 11rem;\n}\n.data-\\[orientation\\=horizontal\\]\\:w-full[data-orientation=\"horizontal\"] {\n  width: 100%;\n}\n.data-\\[orientation\\=vertical\\]\\:w-1\\.5[data-orientation=\"vertical\"] {\n  width: 0.375rem;\n}\n.data-\\[orientation\\=vertical\\]\\:w-auto[data-orientation=\"vertical\"] {\n  width: auto;\n}\n.data-\\[orientation\\=vertical\\]\\:w-full[data-orientation=\"vertical\"] {\n  width: 100%;\n}\n.data-\\[orientation\\=vertical\\]\\:w-px[data-orientation=\"vertical\"] {\n  width: 1px;\n}\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\][data-state=\"checked\"] {\n  --tw-translate-x: calc(100% - 2px);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.data-\\[orientation\\=vertical\\]\\:flex-col[data-orientation=\"vertical\"] {\n  flex-direction: column;\n}\n.data-\\[state\\=checked\\]\\:border-primary[data-state=\"checked\"] {\n  border-color: hsl(var(--primary));\n}\n.data-\\[active\\=true\\]\\:bg-accent\\/50[data-active=\"true\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"] {\n  background-color: hsl(var(--background));\n}\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\n.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=\"open\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\n.data-\\[inset\\]\\:pl-8[data-inset] {\n  padding-left: 2rem;\n}\n.data-\\[active\\=true\\]\\:text-accent-foreground[data-active=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\n.data-\\[placeholder\\]\\:text-muted-foreground[data-placeholder] {\n  color: hsl(var(--muted-foreground));\n}\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"] {\n  color: hsl(var(--accent-foreground));\n}\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\n.data-\\[variant\\=destructive\\]\\:text-destructive[data-variant=\"destructive\"] {\n  color: hsl(var(--destructive));\n}\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"] {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"] {\n  --tw-enter-opacity: 0;\n}\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"] {\n  --tw-exit-opacity: 0;\n}\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"] {\n  --tw-exit-opacity: 0;\n}\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"] {\n  --tw-enter-opacity: 0;\n}\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"] {\n  --tw-enter-scale: .9;\n}\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"] {\n  --tw-enter-translate-x: 13rem;\n}\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"] {\n  --tw-enter-translate-x: -13rem;\n}\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"] {\n  --tw-exit-translate-x: 13rem;\n}\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"] {\n  --tw-exit-translate-x: -13rem;\n}\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1[data-slot=\"select-value\"] > * {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:flex[data-slot=\"select-value\"] > * {\n  display: flex;\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:items-center[data-slot=\"select-value\"] > * {\n  align-items: center;\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:gap-2[data-slot=\"select-value\"] > * {\n  gap: 0.5rem;\n}\n.\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90[data-slot=\"alert-description\"] > * {\n  color: hsl(var(--destructive) / 0.9);\n}\n.data-\\[active\\=true\\]\\:hover\\:bg-accent:hover[data-active=\"true\"] {\n  background-color: hsl(var(--accent));\n}\n.data-\\[state\\=open\\]\\:hover\\:bg-accent:hover[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\n.data-\\[active\\=true\\]\\:focus\\:bg-accent:focus[data-active=\"true\"] {\n  background-color: hsl(var(--accent));\n}\n.data-\\[state\\=open\\]\\:focus\\:bg-accent:focus[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\n.data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10:focus[data-variant=\"destructive\"] {\n  background-color: hsl(var(--destructive) / 0.1);\n}\n.data-\\[variant\\=destructive\\]\\:focus\\:text-destructive:focus[data-variant=\"destructive\"] {\n  color: hsl(var(--destructive));\n}\n.group[data-disabled=\"true\"] .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n  pointer-events: none;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full {\n  top: 100%;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5 {\n  margin-top: 0.375rem;\n}\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden {\n  overflow: hidden;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border {\n  border-width: 1px;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-popover {\n  background-color: hsl(var(--popover));\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\n.group[data-disabled=\"true\"] .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n  opacity: 0.5;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200 {\n  transition-duration: 200ms;\n  animation-duration: 200ms;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\n.dark\\:-rotate-90:is(.dark *) {\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.dark\\:rotate-0:is(.dark *) {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.dark\\:scale-0:is(.dark *) {\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.dark\\:scale-100:is(.dark *) {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.dark\\:border-blue-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-gray-600:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-gray-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-gray-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-green-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-input:is(.dark *) {\n  border-color: hsl(var(--input));\n}\n.dark\\:border-orange-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-purple-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-red-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-white:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-yellow-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\n}\n.dark\\:bg-blue-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-blue-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-blue-900\\/10:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.1);\n}\n.dark\\:bg-blue-900\\/20:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\n.dark\\:bg-blue-900\\/50:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.5);\n}\n.dark\\:bg-blue-950\\/30:is(.dark *) {\n  background-color: rgb(23 37 84 / 0.3);\n}\n.dark\\:bg-destructive\\/60:is(.dark *) {\n  background-color: hsl(var(--destructive) / 0.6);\n}\n.dark\\:bg-gray-600:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-700:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-800\\/50:is(.dark *) {\n  background-color: rgb(31 41 55 / 0.5);\n}\n.dark\\:bg-gray-800\\/80:is(.dark *) {\n  background-color: rgb(31 41 55 / 0.8);\n}\n.dark\\:bg-gray-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-900\\/20:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.2);\n}\n.dark\\:bg-gray-900\\/95:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.95);\n}\n.dark\\:bg-green-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-green-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-green-900\\/20:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.2);\n}\n.dark\\:bg-indigo-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(49 46 129 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-input\\/30:is(.dark *) {\n  background-color: hsl(var(--input) / 0.3);\n}\n.dark\\:bg-orange-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-orange-900\\/20:is(.dark *) {\n  background-color: rgb(124 45 18 / 0.2);\n}\n.dark\\:bg-pink-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(131 24 67 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-purple-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-purple-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-purple-900\\/20:is(.dark *) {\n  background-color: rgb(88 28 135 / 0.2);\n}\n.dark\\:bg-red-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-red-900\\/20:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.2);\n}\n.dark\\:bg-teal-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(19 78 74 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-yellow-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-yellow-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-yellow-900\\/20:is(.dark *) {\n  background-color: rgb(113 63 18 / 0.2);\n}\n.dark\\:bg-red-900\\/40:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.4);\n}\n.dark\\:from-blue-900:is(.dark *) {\n  --tw-gradient-from: #1e3a8a var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-blue-900\\/20:is(.dark *) {\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-gray-700:is(.dark *) {\n  --tw-gradient-from: #374151 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-gray-800:is(.dark *) {\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-gray-900:is(.dark *) {\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-purple-900:is(.dark *) {\n  --tw-gradient-from: #581c87 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-rose-900:is(.dark *) {\n  --tw-gradient-from: #881337 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(136 19 55 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:via-gray-600:is(.dark *) {\n  --tw-gradient-to: rgb(75 85 99 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #4b5563 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:via-gray-800:is(.dark *) {\n  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:to-gray-600:is(.dark *) {\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n.dark\\:to-gray-700:is(.dark *) {\n  --tw-gradient-to: #374151 var(--tw-gradient-to-position);\n}\n.dark\\:to-gray-800:is(.dark *) {\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\n}\n.dark\\:to-gray-900:is(.dark *) {\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\n}\n.dark\\:to-orange-900:is(.dark *) {\n  --tw-gradient-to: #7c2d12 var(--tw-gradient-to-position);\n}\n.dark\\:to-pink-900:is(.dark *) {\n  --tw-gradient-to: #831843 var(--tw-gradient-to-position);\n}\n.dark\\:to-purple-900:is(.dark *) {\n  --tw-gradient-to: #581c87 var(--tw-gradient-to-position);\n}\n.dark\\:to-purple-900\\/20:is(.dark *) {\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:text-blue-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-900:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-500:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(220 252 231 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-indigo-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(165 180 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-muted-foreground:is(.dark *) {\n  color: hsl(var(--muted-foreground));\n}\n.dark\\:text-orange-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 215 170 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-orange-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-orange-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-pink-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(249 168 212 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-purple-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(233 213 255 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-purple-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-purple-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 226 226 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-teal-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(45 212 191 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-white:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 249 195 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.dark\\:ring-blue-800:is(.dark *) {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));\n}\n.dark\\:hover\\:border-blue-400:hover:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:bg-accent\\/50:hover:is(.dark *) {\n  background-color: hsl(var(--accent) / 0.5);\n}\n.dark\\:hover\\:bg-blue-900\\/20:hover:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\n.dark\\:hover\\:bg-gray-700:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-gray-800:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-input\\/50:hover:is(.dark *) {\n  background-color: hsl(var(--input) / 0.5);\n}\n.dark\\:hover\\:bg-white\\/10:hover:is(.dark *) {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.dark\\:hover\\:text-blue-300:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-blue-400:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-gray-300:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.dark\\:focus-visible\\:ring-destructive\\/40:focus-visible:is(.dark *) {\n  --tw-ring-color: hsl(var(--destructive) / 0.4);\n}\n.dark\\:data-\\[state\\=active\\]\\:border-input[data-state=\"active\"]:is(.dark *) {\n  border-color: hsl(var(--input));\n}\n.dark\\:data-\\[state\\=active\\]\\:bg-input\\/30[data-state=\"active\"]:is(.dark *) {\n  background-color: hsl(var(--input) / 0.3);\n}\n.dark\\:data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"]:is(.dark *) {\n  background-color: hsl(var(--primary));\n}\n.dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground[data-state=\"checked\"]:is(.dark *) {\n  background-color: hsl(var(--primary-foreground));\n}\n.dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground[data-state=\"unchecked\"]:is(.dark *) {\n  background-color: hsl(var(--foreground));\n}\n.dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80[data-state=\"unchecked\"]:is(.dark *) {\n  background-color: hsl(var(--input) / 0.8);\n}\n.dark\\:data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"]:is(.dark *) {\n  color: hsl(var(--foreground));\n}\n.dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20:focus[data-variant=\"destructive\"]:is(.dark *) {\n  background-color: hsl(var(--destructive) / 0.2);\n}\n@media (min-width: 640px) {\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:inline-block {\n    display: inline-block;\n  }\n\n  .sm\\:flex {\n    display: flex;\n  }\n\n  .sm\\:max-w-lg {\n    max-width: -moz-max-content;\n    max-width: max-content;\n  }\n\n  .sm\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n}\n@media (min-width: 768px) {\n\n  .md\\:absolute {\n    position: absolute;\n  }\n\n  .md\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:h-24 {\n    height: 6rem;\n  }\n\n  .md\\:h-96 {\n    height: 24rem;\n  }\n\n  .md\\:w-24 {\n    width: 6rem;\n  }\n\n  .md\\:w-48 {\n    width: 12rem;\n  }\n\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    width: var(--radix-navigation-menu-viewport-width);\n  }\n\n  .md\\:w-auto {\n    width: auto;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .md\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .md\\:text-right {\n    text-align: right;\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n}\n@media (min-width: 1024px) {\n\n  .lg\\:col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n\n  .lg\\:flex-row {\n    flex-direction: row;\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .lg\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-7xl {\n    font-size: 4.5rem;\n    line-height: 1;\n  }\n}\n@media (min-width: 1280px) {\n\n  .xl\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n}\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\n.\\[\\&\\>svg\\]\\:pointer-events-none>svg {\n  pointer-events: none;\n}\n.\\[\\&\\>svg\\]\\:size-3>svg {\n  width: 0.75rem;\n  height: 0.75rem;\n}\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\n.\\[\\&\\>svg\\]\\:translate-y-0\\.5>svg {\n  --tw-translate-y: 0.125rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\[\\&\\>svg\\]\\:text-current>svg {\n  color: currentColor;\n}\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\n.\\[\\&_p\\]\\:leading-relaxed p {\n  line-height: 1.625;\n}\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 svg:not([class*='size-']) {\n  width: 1rem;\n  height: 1rem;\n}\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground svg:not([class*='text-']) {\n  color: hsl(var(--muted-foreground));\n}\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}\na.\\[a\\&\\]\\:hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\na.\\[a\\&\\]\\:hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\na.\\[a\\&\\]\\:hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\na.\\[a\\&\\]\\:hover\\:bg-secondary\\/90:hover {\n  background-color: hsl(var(--secondary) / 0.9);\n}\na.\\[a\\&\\]\\:hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;AASA;;;;AAeA;;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;AASA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAAA;;;;;AAeA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGE;;;;AAGA;;;;;;AAOA;;;;AAKA;;;;;AAMA;;;;AAuBA;;;;AAMA;;;;;AAOA;;;;AAMA;;;;;AAOA;;;;AAMA;;;;;AAQA;;;;AAGF;;;;;;;;AAOA;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAYA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;AAOA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;AASA;;;;AAOA;;;;AAOA;;;;AAGA;EACE;;;;;AAIF;EACE;;;;;AAIF;EACE;;;;;AAIF;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAAA;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAQA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;;AA2BA;;;;;;;;;;AA2BA;;;;AAGA;;;;AASA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAYA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;;AAIF;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA", "debugId": null}}]}