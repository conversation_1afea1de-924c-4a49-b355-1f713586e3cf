"use client"

import { useState } from 'react'
import { useCart } from '@/contexts/CartContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { PageLayout } from '@/components/layouts/PageLayout'
import { EnhancedImage } from '@/components/ui/enhanced-image'
import {
  Heart,
  ShoppingCart,
  Trash2,
  ShoppingBag,
  Eye
} from 'lucide-react'

export default function WishlistPage() {
  const { 
    wishlistItems, 
    wishlistCount, 
    removeFromWishlist, 
    clearWishlist,
    addToCart 
  } = useCart()

  const handleRemoveItem = (productId: string) => {
    removeFromWishlist(productId)
  }

  const handleClearWishlist = () => {
    clearWishlist()
  }

  const handleAddToCart = (item: any, type: 'purchase' | 'rental' = 'purchase') => {
    addToCart(item.id, item, type)
  }

  if (wishlistCount === 0) {
    return (
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <Heart className="h-24 w-24 text-gray-400 mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            المفضلة فارغة
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8 arabic-text">
            لم تقم بإضافة أي منتجات للمفضلة بعد
          </p>
          <Button asChild>
            <a href="/catalog">
              <ShoppingBag className="h-4 w-4 mr-2" />
              تصفح المنتجات
            </a>
          </Button>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            ❤️ المفضلة
          </h1>
          <p className="text-gray-600 dark:text-gray-400 arabic-text">
            لديك {wishlistCount} منتج في المفضلة
          </p>
        </div>

        <div className="space-y-6">
          {/* Clear Wishlist Button */}
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={handleClearWishlist}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              إفراغ المفضلة
            </Button>
          </div>

          {/* Wishlist Items Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlistItems.map((item) => (
              <Card key={item.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <EnhancedImage
                      src={item.image}
                      alt={item.name}
                      width={300}
                      height={300}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    
                    {/* Remove from Wishlist Button */}
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                      onClick={() => handleRemoveItem(item.id)}
                    >
                      <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="p-4">
                  <CardTitle className="text-lg font-semibold mb-2 arabic-text line-clamp-1">
                    {item.name}
                  </CardTitle>

                  {/* Prices */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-blue-600">
                        {item.price} Dhs
                      </span>
                      {item.rental_price && (
                        <span className="text-sm text-gray-500">
                          إيجار: {item.rental_price} Dhs
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    {/* أزرار الشراء والإيجار */}
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        className="flex-1"
                        onClick={() => handleAddToCart(item, 'purchase')}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        شراء
                      </Button>
                      
                      {item.rental_price && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          className="flex-1"
                          onClick={() => handleAddToCart(item, 'rental')}
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          إيجار
                        </Button>
                      )}
                    </div>
                    
                    {/* زر عرض التفاصيل */}
                    <Button size="sm" variant="outline" className="w-full" asChild>
                      <a href={`/product/${item.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        عرض التفاصيل
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Continue Shopping */}
          <div className="text-center pt-8">
            <Button asChild>
              <a href="/catalog">
                <ShoppingBag className="h-4 w-4 mr-2" />
                متابعة التسوق
              </a>
            </Button>
          </div>
        </div>
      </PageLayout>
  )
}
