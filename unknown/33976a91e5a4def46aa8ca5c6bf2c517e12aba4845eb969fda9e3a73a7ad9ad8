-- قاعدة بيانات منصة أزياء التخرج المغربية - Graduation Toqs
-- إعداد شامل وحديث للمشروع

-- تفعيل الإضافات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إنشاء الأنواع المخصصة
CREATE TYPE user_role AS ENUM ('student', 'school', 'admin', 'delivery');
CREATE TYPE order_status AS ENUM ('pending', 'approved', 'processing', 'shipped', 'delivered', 'cancelled', 'returned');
CREATE TYPE order_type AS ENUM ('rental', 'purchase');
CREATE TYPE product_category AS ENUM ('gown', 'cap', 'tassel', 'stole', 'hood', 'accessories');
CREATE TYPE notification_type AS ENUM ('info', 'success', 'warning', 'error', 'order', 'system');
CREATE TYPE menu_target_type AS ENUM ('internal', 'external', 'page');
CREATE TYPE language_code AS ENUM ('ar', 'en', 'fr');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');

-- جدول الملفات التعريفية للمستخدمين
CREATE TABLE profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'student',
    phone TEXT,
    school_name TEXT,
    avatar_url TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female')),
    address TEXT,
    city TEXT,
    country TEXT DEFAULT 'Morocco',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المدارس
CREATE TABLE schools (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    name_en TEXT,
    name_fr TEXT,
    address TEXT,
    city TEXT,
    phone TEXT,
    email TEXT,
    website TEXT,
    logo_url TEXT,
    graduation_date DATE,
    student_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المنتجات
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    name_en TEXT,
    name_fr TEXT,
    description TEXT,
    description_en TEXT,
    description_fr TEXT,
    category product_category NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    rental_price DECIMAL(10,2),
    colors TEXT[] DEFAULT '{}',
    sizes TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    stock_quantity INTEGER DEFAULT 0,
    min_stock_level INTEGER DEFAULT 10,
    is_available BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height}
    materials TEXT[],
    care_instructions TEXT,
    features TEXT[] DEFAULT '{}',
    specifications JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[],
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الطلبات
CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_number TEXT UNIQUE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    school_id UUID REFERENCES schools(id),
    status order_status DEFAULT 'pending',
    order_type order_type NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    final_amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'MAD',
    
    -- معلومات التوصيل
    delivery_address TEXT,
    delivery_city TEXT,
    delivery_phone TEXT,
    delivery_date DATE,
    delivery_time_slot TEXT,
    delivery_notes TEXT,
    
    -- معلومات الإرجاع (للإيجار)
    return_date DATE,
    return_address TEXT,
    
    -- معلومات الدفع
    payment_method TEXT,
    payment_status payment_status DEFAULT 'pending',
    payment_reference TEXT,
    
    notes TEXT,
    admin_notes TEXT,
    tracking_number TEXT,
    estimated_delivery DATE,
    
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول عناصر الطلبات
CREATE TABLE order_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL DEFAULT 1,
    size TEXT,
    color TEXT,
    customizations JSONB DEFAULT '{}',
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    rental_start_date DATE,
    rental_end_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    title_en TEXT,
    title_fr TEXT,
    message TEXT NOT NULL,
    message_en TEXT,
    message_fr TEXT,
    type notification_type DEFAULT 'info',
    is_read BOOLEAN DEFAULT false,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول عناصر القائمة الرئيسية
CREATE TABLE menu_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title_ar TEXT NOT NULL,
    title_en TEXT,
    title_fr TEXT,
    slug TEXT UNIQUE NOT NULL,
    icon TEXT, -- Lucide icon name
    parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    target_type menu_target_type NOT NULL DEFAULT 'internal',
    target_value TEXT NOT NULL,
    css_class TEXT,
    permissions TEXT[], -- أدوار المستخدمين المسموح لهم برؤية هذا العنصر
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الصفحات الديناميكية
CREATE TABLE pages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    slug TEXT UNIQUE NOT NULL,
    is_published BOOLEAN DEFAULT false,
    author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    featured_image TEXT,
    template TEXT DEFAULT 'default',
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[],
    view_count INTEGER DEFAULT 0,
    allow_comments BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول محتوى الصفحات متعدد اللغات
CREATE TABLE page_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,
    language language_code NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    meta_description TEXT,
    meta_keywords TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(page_id, language)
);

-- جدول المراجعات والتقييمات
CREATE TABLE reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    title TEXT,
    comment TEXT,
    images TEXT[] DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT false,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, user_id, order_id)
);

-- جدول قائمة الأمنيات
CREATE TABLE wishlist (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- جدول سلة التسوق
CREATE TABLE cart_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    size TEXT,
    color TEXT,
    customizations JSONB DEFAULT '{}',
    rental_start_date DATE,
    rental_end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول كوبونات الخصم
CREATE TABLE coupons (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    discount_type TEXT CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_discount_amount DECIMAL(10,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    applicable_products UUID[],
    applicable_categories product_category[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول استخدام الكوبونات
CREATE TABLE coupon_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    coupon_id UUID REFERENCES coupons(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    discount_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(coupon_id, user_id, order_id)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'general',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_is_available ON products(is_available);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_menu_items_parent_id ON menu_items(parent_id);
CREATE INDEX idx_menu_items_order_index ON menu_items(order_index);
CREATE INDEX idx_pages_slug ON pages(slug);
CREATE INDEX idx_pages_is_published ON pages(is_published);
CREATE INDEX idx_page_content_page_id ON page_content(page_id);
CREATE INDEX idx_page_content_language ON page_content(language);
CREATE INDEX idx_reviews_product_id ON reviews(product_id);
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX idx_wishlist_user_id ON wishlist(user_id);

-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للملفات التعريفية
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات المدارس
CREATE POLICY "School admins can manage their school" ON schools
    FOR ALL USING (admin_id = auth.uid());

CREATE POLICY "Admins can view all schools" ON schools
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Public can view active schools" ON schools
    FOR SELECT USING (is_active = true);

-- سياسات المنتجات
CREATE POLICY "Anyone can view available products" ON products
    FOR SELECT USING (is_available = true);

CREATE POLICY "Admins can manage products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات الطلبات
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (
        user_id = auth.uid() OR
        school_id IN (SELECT id FROM schools WHERE admin_id = auth.uid()) OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'delivery')
        )
    );

CREATE POLICY "Users can create own orders" ON orders
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Authorized users can update orders" ON orders
    FOR UPDATE USING (
        user_id = auth.uid() OR
        school_id IN (SELECT id FROM schools WHERE admin_id = auth.uid()) OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'delivery')
        )
    );

-- سياسات عناصر الطلبات
CREATE POLICY "Users can view own order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders
            WHERE id = order_id AND (
                user_id = auth.uid() OR
                school_id IN (SELECT id FROM schools WHERE admin_id = auth.uid()) OR
                EXISTS (
                    SELECT 1 FROM profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'delivery')
                )
            )
        )
    );

-- سياسات الإشعارات
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all notifications" ON notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات عناصر القائمة
CREATE POLICY "Anyone can view active menu items" ON menu_items
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage menu items" ON menu_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات الصفحات
CREATE POLICY "Anyone can view published pages" ON pages
    FOR SELECT USING (is_published = true);

CREATE POLICY "Admins can manage pages" ON pages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات محتوى الصفحات
CREATE POLICY "Anyone can view published page content" ON page_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM pages
            WHERE id = page_id AND is_published = true
        )
    );

CREATE POLICY "Admins can manage page content" ON page_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات المراجعات
CREATE POLICY "Anyone can view approved reviews" ON reviews
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Users can manage own reviews" ON reviews
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all reviews" ON reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات قائمة الأمنيات
CREATE POLICY "Users can manage own wishlist" ON wishlist
    FOR ALL USING (user_id = auth.uid());

-- سياسات سلة التسوق
CREATE POLICY "Users can manage own cart" ON cart_items
    FOR ALL USING (user_id = auth.uid());

-- سياسات الكوبونات
CREATE POLICY "Anyone can view active coupons" ON coupons
    FOR SELECT USING (is_active = true AND valid_until > NOW());

CREATE POLICY "Admins can manage coupons" ON coupons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات استخدام الكوبونات
CREATE POLICY "Users can view own coupon usage" ON coupon_usage
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create coupon usage" ON coupon_usage
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- سياسات إعدادات النظام
CREATE POLICY "Anyone can view public settings" ON system_settings
    FOR SELECT USING (is_public = true);

CREATE POLICY "Admins can manage all settings" ON system_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- الدوال المساعدة

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة إنشاء رقم طلب فريد
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_num TEXT;
    counter INTEGER;
BEGIN
    -- إنشاء رقم طلب بتنسيق: GT-YYYYMMDD-XXXX
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 12) AS INTEGER)), 0) + 1
    INTO counter
    FROM orders
    WHERE order_number LIKE 'GT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-%';

    order_num := 'GT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(counter::TEXT, 4, '0');

    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- دالة إنشاء ملف تعريفي عند تسجيل مستخدم جديد
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name, role, phone, school_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'مستخدم جديد'),
        COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'student'),
        NEW.raw_user_meta_data->>'phone',
        NEW.raw_user_meta_data->>'school_name'
    );
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- في حالة حدوث خطأ، سجل الخطأ ولكن لا تمنع إنشاء المستخدم
        RAISE WARNING 'خطأ في إنشاء الملف التعريفي للمستخدم %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث تقييم المنتج
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE products
    SET
        rating = (
            SELECT COALESCE(AVG(rating), 0)
            FROM reviews
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
            AND is_approved = true
        ),
        review_count = (
            SELECT COUNT(*)
            FROM reviews
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
            AND is_approved = true
        )
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث عدد الاستخدام للكوبون
CREATE OR REPLACE FUNCTION update_coupon_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE coupons
        SET used_count = used_count + 1
        WHERE id = NEW.coupon_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE coupons
        SET used_count = GREATEST(used_count - 1, 0)
        WHERE id = OLD.coupon_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث عدد المشاهدات للصفحة
CREATE OR REPLACE FUNCTION increment_page_views()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE pages
    SET view_count = view_count + 1
    WHERE id = NEW.page_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات (Triggers)

-- محفز تحديث updated_at
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schools_updated_at
    BEFORE UPDATE ON schools
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_items_updated_at
    BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pages_updated_at
    BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_page_content_updated_at
    BEFORE UPDATE ON page_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at
    BEFORE UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_items_updated_at
    BEFORE UPDATE ON cart_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at
    BEFORE UPDATE ON coupons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- محفز إنشاء المستخدم الجديد
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- محفز تحديث تقييم المنتج
CREATE TRIGGER update_product_rating_on_review_change
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_product_rating();

-- محفز تحديث استخدام الكوبون
CREATE TRIGGER update_coupon_usage_count_trigger
    AFTER INSERT OR DELETE ON coupon_usage
    FOR EACH ROW EXECUTE FUNCTION update_coupon_usage_count();

-- محفز إنشاء رقم طلب تلقائي
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION set_order_number();

-- إدراج البيانات الأولية

-- إدراج عناصر القائمة الرئيسية الافتراضية
INSERT INTO menu_items (title_ar, title_en, title_fr, slug, icon, order_index, target_type, target_value) VALUES
('الرئيسية', 'Home', 'Accueil', 'home', 'Home', 1, 'internal', '/'),
('الكتالوج', 'Catalog', 'Catalogue', 'catalog', 'ShoppingBag', 2, 'internal', '/catalog'),
('من نحن', 'About', 'À Propos', 'about', 'Info', 3, 'internal', '/about'),
('تواصل معنا', 'Contact', 'Contact', 'contact', 'Phone', 4, 'internal', '/contact');

-- إدراج منتجات تجريبية
INSERT INTO products (name, name_en, name_fr, description, description_en, description_fr, category, price, rental_price, colors, sizes, images, stock_quantity, is_available, is_featured) VALUES
(
    'ثوب التخرج الكلاسيكي',
    'Classic Graduation Gown',
    'Toge de Graduation Classique',
    'ثوب تخرج أنيق ومريح مصنوع من أجود الخامات، مناسب لجميع المناسبات الرسمية والاحتفالات الأكاديمية',
    'Elegant and comfortable graduation gown made from premium materials, suitable for all formal occasions and academic celebrations',
    'Toge de graduation élégante et confortable fabriquée à partir de matériaux de qualité supérieure, adaptée à toutes les occasions formelles et célébrations académiques',
    'gown',
    299.99,
    99.99,
    ARRAY['أسود', 'أزرق داكن', 'بورجوندي', 'أخضر داكن'],
    ARRAY['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    ARRAY['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],
    50,
    true,
    true
),
(
    'قبعة التخرج التقليدية',
    'Traditional Graduation Cap',
    'Chapeau de Graduation Traditionnel',
    'قبعة تخرج كلاسيكية مع شرابة، مصنوعة من مواد عالية الجودة لضمان الراحة والأناقة',
    'Classic graduation cap with tassel, made from high-quality materials to ensure comfort and elegance',
    'Chapeau de graduation classique avec pompon, fabriqué à partir de matériaux de haute qualité pour assurer confort et élégance',
    'cap',
    79.99,
    29.99,
    ARRAY['أسود', 'أزرق داكن'],
    ARRAY['واحد'],
    ARRAY['/images/products/cap-traditional-1.jpg'],
    100,
    true,
    true
),
(
    'وشاح التخرج المطرز',
    'Embroidered Graduation Stole',
    'Étole de Graduation Brodée',
    'وشاح أنيق مطرز بتفاصيل ذهبية فاخرة، يضيف لمسة من الفخامة لإطلالة التخرج',
    'Elegant stole embroidered with luxurious golden details, adding a touch of luxury to the graduation look',
    'Étole élégante brodée de détails dorés luxueux, ajoutant une touche de luxe au look de graduation',
    'stole',
    149.99,
    49.99,
    ARRAY['ذهبي', 'فضي', 'أبيض', 'أزرق'],
    ARRAY['واحد'],
    ARRAY['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],
    30,
    true,
    false
),
(
    'شرابة التخرج الملونة',
    'Colored Graduation Tassel',
    'Pompon de Graduation Coloré',
    'شرابة ملونة عالية الجودة لقبعة التخرج، متوفرة بألوان متعددة لتناسب جميع التخصصات',
    'High-quality colored tassel for graduation cap, available in multiple colors to suit all specializations',
    'Pompon coloré de haute qualité pour chapeau de graduation, disponible en plusieurs couleurs pour convenir à toutes les spécialisations',
    'tassel',
    19.99,
    9.99,
    ARRAY['ذهبي', 'فضي', 'أسود', 'أزرق', 'أحمر', 'أخضر'],
    ARRAY['واحد'],
    ARRAY['/images/products/tassel-colored-1.jpg'],
    200,
    true,
    false
);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (key, value, description, category, is_public) VALUES
('site_name', '{"ar": "منصة أزياء التخرج", "en": "Graduation Toqs Platform", "fr": "Plateforme Toqs de Graduation"}', 'اسم الموقع', 'general', true),
('site_description', '{"ar": "منصة متخصصة في تأجير وبيع أزياء التخرج المغربية", "en": "Specialized platform for renting and selling Moroccan graduation attire", "fr": "Plateforme spécialisée dans la location et la vente de tenues de graduation marocaines"}', 'وصف الموقع', 'general', true),
('contact_email', '"<EMAIL>"', 'البريد الإلكتروني للتواصل', 'contact', true),
('contact_phone', '"+212 5XX-XXXXXX"', 'رقم الهاتف للتواصل', 'contact', true),
('default_currency', '"MAD"', 'العملة الافتراضية', 'payment', true),
('tax_rate', '0.20', 'معدل الضريبة (20%)', 'payment', false),
('shipping_cost', '50.00', 'تكلفة الشحن الافتراضية', 'shipping', false),
('free_shipping_threshold', '500.00', 'الحد الأدنى للشحن المجاني', 'shipping', true),
('rental_duration_days', '7', 'مدة الإيجار الافتراضية بالأيام', 'rental', true),
('return_policy_days', '3', 'مدة سياسة الإرجاع بالأيام', 'policy', true),
('maintenance_mode', 'false', 'وضع الصيانة', 'system', false),
('allow_registration', 'true', 'السماح بالتسجيل الجديد', 'system', false),
('email_notifications', 'true', 'تفعيل الإشعارات عبر البريد الإلكتروني', 'notifications', false),
('sms_notifications', 'false', 'تفعيل الإشعارات عبر الرسائل النصية', 'notifications', false);

-- Note: Admin profile will be created separately after auth user exists
-- Use fix-admin-creation.sql after creating the auth user in Supabase Dashboard

-- إنشاء صفحة ترحيب تجريبية
DO $$
DECLARE
    welcome_page_id uuid;
    admin_id uuid;
BEGIN
    -- الحصول على معرف المدير
    SELECT id INTO admin_id FROM profiles WHERE email = '<EMAIL>';

    -- إنشاء صفحة ترحيب
    INSERT INTO pages (
        slug,
        is_published,
        author_id,
        template,
        seo_title,
        seo_description,
        created_at,
        updated_at
    ) VALUES (
        'welcome',
        true,
        admin_id,
        'default',
        'مرحباً بكم في منصة أزياء التخرج',
        'منصة متخصصة في تأجير وبيع أزياء التخرج المغربية',
        NOW(),
        NOW()
    ) RETURNING id INTO welcome_page_id;

    -- إضافة محتوى الصفحة بجميع اللغات
    INSERT INTO page_content (
        page_id,
        language,
        title,
        content,
        excerpt,
        meta_description,
        created_at,
        updated_at
    ) VALUES
    (
        welcome_page_id,
        'ar',
        'Welcome to Graduation Attire Platform',
        '<h1>Welcome to Moroccan Graduation Attire Platform</h1>
        <p>We specialize in providing the best graduation attire for students and schools in Morocco. Our platform offers rental and purchase services for all graduation essentials.</p>
        <h2>Our Services</h2>
        <ul>
        <li>Graduation gown rental in various sizes</li>
        <li>Graduation accessories sales</li>
        <li>Fast delivery service</li>
        <li>Excellent technical support</li>
        </ul>
        <p>Start your journey with us today and make your graduation day unforgettable!</p>',
        'Specialized platform for renting and selling Moroccan graduation attire',
        'Moroccan Graduation Attire Platform - Rental and Sale of Graduation Gowns',
        NOW(),
        NOW()
    ),
    (
        welcome_page_id,
        'en',
        'Welcome to Graduation Attire Platform',
        '<h1>Welcome to Moroccan Graduation Attire Platform</h1>
        <p>We specialize in providing the best graduation attire for students and schools in Morocco. Our platform offers rental and purchase services for all graduation essentials.</p>
        <h2>Our Services</h2>
        <ul>
        <li>Graduation gown rental in various sizes</li>
        <li>Graduation accessories sales</li>
        <li>Fast delivery service</li>
        <li>Excellent technical support</li>
        </ul>
        <p>Start your journey with us today and make your graduation day unforgettable!</p>',
        'Specialized platform for renting and selling Moroccan graduation attire',
        'Moroccan Graduation Attire Platform - Rental and Sale of Graduation Gowns',
        NOW(),
        NOW()
    ),
    (
        welcome_page_id,
        'fr',
        'Bienvenue sur la Plateforme de Tenues de Graduation',
        '<h1>Bienvenue sur la Plateforme de Tenues de Graduation Marocaine</h1>
        <p>Nous nous spécialisons dans la fourniture des meilleures tenues de graduation pour les étudiants et les écoles au Maroc. Notre plateforme offre des services de location et d''achat pour tous les essentiels de graduation.</p>
        <h2>Nos Services</h2>
        <ul>
        <li>Location de toges de graduation en diverses tailles</li>
        <li>Vente d''accessoires de graduation</li>
        <li>Service de livraison rapide</li>
        <li>Support technique excellent</li>
        </ul>
        <p>Commencez votre voyage avec nous aujourd''hui et rendez votre jour de graduation inoubliable!</p>',
        'Plateforme spécialisée dans la location et la vente de tenues de graduation marocaines',
        'Plateforme de Tenues de Graduation Marocaine - Location et Vente de Toges',
        NOW(),
        NOW()
    );

    RAISE NOTICE 'تم إنشاء صفحة ترحيب: /pages/welcome';
END $$;

-- عرض ملخص الإعداد
SELECT
    'تم إعداد قاعدة البيانات بنجاح!' as status,
    (SELECT COUNT(*) FROM menu_items) as menu_items_count,
    (SELECT COUNT(*) FROM products) as products_count,
    (SELECT COUNT(*) FROM pages) as pages_count,
    (SELECT COUNT(*) FROM page_content) as page_content_count,
    (SELECT COUNT(*) FROM system_settings) as settings_count,
    (SELECT COUNT(*) FROM profiles WHERE role = 'admin') as admin_count;

-- تعليمات المتابعة
SELECT
    'الخطوات التالية:' as next_steps,
    '1. أنشئ مستخدم في Supabase Dashboard بالإيميل: <EMAIL>' as step_1,
    '2. استخدم كلمة سر قوية مثل: GradAdmin2024!@#' as step_2,
    '3. فعّل Auto Confirm User في إعدادات المستخدم' as step_3,
    '4. شغل ملف setup-admin.sql لربط المستخدم بالملف التعريفي' as step_4,
    '5. سجل دخول واختبر النظام' as step_5;
