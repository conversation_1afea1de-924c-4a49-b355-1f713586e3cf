import { NextRequest, NextResponse } from 'next/server'
import { UserModel } from '@/lib/models/User'
import { checkDatabaseHealth } from '@/lib/database'

// GET - جلب جميع المستخدمين
export async function GET(request: NextRequest) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json(
        { error: 'قاعدة البيانات غير متاحة' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    
    // استخراج معاملات الفلترة
    const filters = {
      role: searchParams.get('role') || undefined,
      is_active: searchParams.get('is_active') === 'true' ? true : 
                 searchParams.get('is_active') === 'false' ? false : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0
    }

    // جلب المستخدمين من قاعدة البيانات
    const result = await UserModel.getAll(filters)

    return NextResponse.json({
      users: result.users,
      total: result.total,
      page: Math.floor(filters.offset / filters.limit) + 1,
      totalPages: Math.ceil(result.total / filters.limit),
      filters: filters
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'فشل في جلب المستخدمين' },
      { status: 500 }
    )
  }
}

// POST - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من البيانات المطلوبة
    if (!body.email || !body.password || !body.first_name || !body.last_name) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني وكلمة المرور والاسم الأول والأخير مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من قوة كلمة المرور
    if (body.password.length < 8) {
      return NextResponse.json(
        { error: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await UserModel.findByEmail(body.email)
    if (existingUser) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني مستخدم بالفعل' },
        { status: 409 }
      )
    }

    // إنشاء المستخدم
    const user = await UserModel.create({
      email: body.email,
      password: body.password,
      first_name: body.first_name,
      last_name: body.last_name,
      phone: body.phone,
      role: body.role || 'customer'
    })

    return NextResponse.json({
      message: 'تم إنشاء المستخدم بنجاح',
      user: user
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'فشل في إنشاء المستخدم' },
      { status: 500 }
    )
  }
}
