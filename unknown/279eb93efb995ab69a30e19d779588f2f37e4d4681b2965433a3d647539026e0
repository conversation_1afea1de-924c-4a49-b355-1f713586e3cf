// إعدادات صفحة الدفع القابلة للتخصيص
export interface CheckoutField {
  id: string
  name: string
  label: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox'
  required: boolean
  enabled: boolean
  placeholder?: string
  options?: string[] // للحقول من نوع select
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
  }
  order: number
  section: 'shipping' | 'billing' | 'personal'
}

export interface PaymentMethodConfig {
  id: string
  name: string
  description: string
  icon: string
  enabled: boolean
  order: number
  config?: {
    requiresCard?: boolean
    requiresBankDetails?: boolean
    additionalFields?: CheckoutField[]
  }
}

export interface DeliveryOption {
  id: string
  name: string
  description: string
  price: number
  estimatedDays: string
  enabled: boolean
  order: number
  icon: string
  restrictions?: {
    minOrderValue?: number
    maxOrderValue?: number
    availableRegions?: string[]
  }
}

export interface CheckoutSettings {
  fields: CheckoutField[]
  paymentMethods: PaymentMethodConfig[]
  deliveryOptions: DeliveryOption[]
  general: {
    requireTermsAcceptance: boolean
    allowGuestCheckout: boolean
    showOrderSummary: boolean
    enableSpecialInstructions: boolean
    defaultCountry: string
    currency: string
    taxRate: number
  }
}

// الإعدادات الافتراضية
export const defaultCheckoutSettings: CheckoutSettings = {
  fields: [
    {
      id: 'fullName',
      name: 'fullName',
      label: 'الاسم الكامل',
      type: 'text',
      required: true,
      enabled: true,
      placeholder: 'أدخل اسمك الكامل',
      order: 1,
      section: 'personal',
      validation: { minLength: 2, maxLength: 100 }
    },
    {
      id: 'email',
      name: 'email',
      label: 'البريد الإلكتروني',
      type: 'email',
      required: true,
      enabled: true,
      placeholder: '<EMAIL>',
      order: 2,
      section: 'personal'
    },
    {
      id: 'phone',
      name: 'phone',
      label: 'رقم الهاتف',
      type: 'tel',
      required: true,
      enabled: true,
      placeholder: '+971-XX-XXX-XXXX',
      order: 3,
      section: 'personal'
    },
    {
      id: 'address',
      name: 'address',
      label: 'العنوان',
      type: 'textarea',
      required: true,
      enabled: true,
      placeholder: 'أدخل عنوانك الكامل',
      order: 4,
      section: 'shipping'
    },
    {
      id: 'city',
      name: 'city',
      label: 'المدينة',
      type: 'text',
      required: true,
      enabled: true,
      placeholder: 'اسم المدينة',
      order: 5,
      section: 'shipping'
    },
    {
      id: 'state',
      name: 'state',
      label: 'الإمارة/المنطقة',
      type: 'select',
      required: true,
      enabled: true,
      order: 6,
      section: 'shipping',
      options: ['أبوظبي', 'دبي', 'الشارقة', 'عجمان', 'أم القيوين', 'رأس الخيمة', 'الفجيرة']
    },
    {
      id: 'zipCode',
      name: 'zipCode',
      label: 'الرمز البريدي',
      type: 'text',
      required: false,
      enabled: true,
      placeholder: '12345',
      order: 7,
      section: 'shipping'
    },
    {
      id: 'specialInstructions',
      name: 'specialInstructions',
      label: 'تعليمات خاصة',
      type: 'textarea',
      required: false,
      enabled: true,
      placeholder: 'أي تعليمات خاصة للتوصيل...',
      order: 8,
      section: 'shipping'
    }
  ],
  
  paymentMethods: [
    {
      id: 'card',
      name: 'بطاقة ائتمان/خصم',
      description: 'Visa, Mastercard, American Express',
      icon: 'CreditCard',
      enabled: true,
      order: 1,
      config: {
        requiresCard: true,
        additionalFields: [
          {
            id: 'cardNumber',
            name: 'cardNumber',
            label: 'رقم البطاقة',
            type: 'text',
            required: true,
            enabled: true,
            placeholder: '1234 5678 9012 3456',
            order: 1,
            section: 'billing'
          },
          {
            id: 'expiryDate',
            name: 'expiryDate',
            label: 'تاريخ الانتهاء',
            type: 'text',
            required: true,
            enabled: true,
            placeholder: 'MM/YY',
            order: 2,
            section: 'billing'
          },
          {
            id: 'cvv',
            name: 'cvv',
            label: 'رمز الأمان',
            type: 'text',
            required: true,
            enabled: true,
            placeholder: '123',
            order: 3,
            section: 'billing'
          }
        ]
      }
    },
    {
      id: 'cash',
      name: 'الدفع عند الاستلام',
      description: 'ادفع نقداً عند وصول الطلب',
      icon: 'Banknote',
      enabled: true,
      order: 2
    },
    {
      id: 'bank_transfer',
      name: 'تحويل بنكي',
      description: 'تحويل مباشر إلى حساب البنك',
      icon: 'Building2',
      enabled: true,
      order: 3
    },
    {
      id: 'digital_wallet',
      name: 'المحفظة الرقمية',
      description: 'Apple Pay, Google Pay, Samsung Pay',
      icon: 'Smartphone',
      enabled: false,
      order: 4
    }
  ],
  
  deliveryOptions: [
    {
      id: 'standard',
      name: 'التوصيل العادي',
      description: '3-5 أيام عمل',
      price: 25,
      estimatedDays: '3-5 أيام',
      enabled: true,
      order: 1,
      icon: 'Truck'
    },
    {
      id: 'express',
      name: 'التوصيل السريع',
      description: '1-2 أيام عمل',
      price: 50,
      estimatedDays: '1-2 أيام',
      enabled: true,
      order: 2,
      icon: 'Zap',
      restrictions: {
        minOrderValue: 100
      }
    },
    {
      id: 'same_day',
      name: 'التوصيل في نفس اليوم',
      description: 'خلال 6 ساعات',
      price: 100,
      estimatedDays: '6 ساعات',
      enabled: true,
      order: 3,
      icon: 'Clock',
      restrictions: {
        minOrderValue: 200,
        availableRegions: ['دبي', 'أبوظبي']
      }
    },
    {
      id: 'pickup',
      name: 'الاستلام من المتجر',
      description: 'استلم طلبك من فرعنا',
      price: 0,
      estimatedDays: 'فوري',
      enabled: false,
      order: 4,
      icon: 'Store'
    }
  ],
  
  general: {
    requireTermsAcceptance: true,
    allowGuestCheckout: true,
    showOrderSummary: true,
    enableSpecialInstructions: true,
    defaultCountry: 'الإمارات العربية المتحدة',
    currency: 'AED',
    taxRate: 0.05
  }
}

// مدير إعدادات الدفع
export class CheckoutSettingsManager {
  private static getStorageKey(): string {
    return 'checkoutSettings'
  }

  static getSettings(): CheckoutSettings {
    if (typeof window === 'undefined') return defaultCheckoutSettings

    const stored = localStorage.getItem(this.getStorageKey())
    return stored ? JSON.parse(stored) : defaultCheckoutSettings
  }

  static saveSettings(settings: CheckoutSettings): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.getStorageKey(), JSON.stringify(settings))
  }

  static resetToDefaults(): void {
    this.saveSettings(defaultCheckoutSettings)
  }

  // إدارة الحقول
  static addField(field: CheckoutField): void {
    const settings = this.getSettings()
    settings.fields.push(field)
    this.saveSettings(settings)
  }

  static updateField(fieldId: string, updates: Partial<CheckoutField>): void {
    const settings = this.getSettings()
    const fieldIndex = settings.fields.findIndex(f => f.id === fieldId)
    if (fieldIndex !== -1) {
      settings.fields[fieldIndex] = { ...settings.fields[fieldIndex], ...updates }
      this.saveSettings(settings)
    }
  }

  static removeField(fieldId: string): void {
    const settings = this.getSettings()
    settings.fields = settings.fields.filter(f => f.id !== fieldId)
    this.saveSettings(settings)
  }

  // إدارة طرق الدفع
  static updatePaymentMethod(methodId: string, updates: Partial<PaymentMethodConfig>): void {
    const settings = this.getSettings()
    const methodIndex = settings.paymentMethods.findIndex(m => m.id === methodId)
    if (methodIndex !== -1) {
      settings.paymentMethods[methodIndex] = { ...settings.paymentMethods[methodIndex], ...updates }
      this.saveSettings(settings)
    }
  }

  // إدارة خيارات التوصيل
  static updateDeliveryOption(optionId: string, updates: Partial<DeliveryOption>): void {
    const settings = this.getSettings()
    const optionIndex = settings.deliveryOptions.findIndex(o => o.id === optionId)
    if (optionIndex !== -1) {
      settings.deliveryOptions[optionIndex] = { ...settings.deliveryOptions[optionIndex], ...updates }
      this.saveSettings(settings)
    }
  }
}
