# 🚀 دليل البدء السريع - منصة أزياء التخرج المغربية

## ⚡ تشغيل المشروع في 3 خطوات

### 1. تثبيت التبعيات
```bash
cd frontend
npm install
```

### 2. تشغيل الخادم
```bash
npm run dev
```

### 3. فتح المتصفح
```
http://localhost:3000
```

---

## 🎯 الصفحات الرئيسية

### للمستخدمين العاديين:
- **الصفحة الرئيسية**: http://localhost:3000
- **الكتالوج**: http://localhost:3000/catalog
- **السلة**: http://localhost:3000/cart
- **إتمام الطلب**: http://localhost:3000/checkout

### الصفحات الجديدة:
- **الشروط والأحكام**: http://localhost:3000/terms-conditions
- **سياسة الخصوصية**: http://localhost:3000/privacy-policy
- **الأسئلة الشائعة**: http://localhost:3000/faq
- **الدفع البنكي**: http://localhost:3000/payment/bank-transfer
- **تأكيد الدفع**: http://localhost:3000/payment/confirmation

### للإدارة:
- **لوحة التحكم**: http://localhost:3000/dashboard/admin
- **إدارة المنتجات**: http://localhost:3000/dashboard/admin/products
- **إدارة الطلبات**: http://localhost:3000/dashboard/admin/orders
- **إدارة طرق الدفع**: http://localhost:3000/dashboard/admin/payment-methods
- **إدارة التوصيل**: http://localhost:3000/dashboard/admin/delivery

---

## 🔧 إصلاح المشاكل الشائعة

### المشكلة: "npm run dev" لا يعمل
```bash
# تأكد من أنك في المجلد الصحيح
cd frontend

# تثبيت التبعيات مرة أخرى
npm install

# تشغيل الخادم
npm run dev
```

### المشكلة: صفحة فارغة أو أخطاء
```bash
# تحقق من الكونسول في المتصفح (F12)
# تأكد من تشغيل الخادم على المنفذ الصحيح
# أعد تشغيل الخادم
```

### المشكلة: أخطاء TypeScript
```bash
# تحقق من الملفات المحدثة
# تأكد من صحة الاستيرادات
# أعد تشغيل الخادم
```

---

## 📁 بنية المشروع

```
frontend/
├── src/
│   ├── app/                    # صفحات Next.js
│   │   ├── page.tsx           # الصفحة الرئيسية
│   │   ├── catalog/           # صفحة الكتالوج
│   │   ├── cart/              # صفحة السلة
│   │   ├── checkout/          # صفحة إتمام الطلب
│   │   ├── terms-conditions/  # 🆕 الشروط والأحكام
│   │   ├── privacy-policy/    # 🆕 سياسة الخصوصية
│   │   ├── faq/               # 🆕 الأسئلة الشائعة
│   │   ├── payment/           # 🆕 صفحات الدفع
│   │   └── dashboard/         # لوحات التحكم
│   ├── components/            # المكونات المشتركة
│   ├── contexts/              # Context للحالة العامة
│   ├── hooks/                 # Custom Hooks
│   └── types/                 # أنواع TypeScript
├── public/                    # الملفات العامة
└── package.json              # تبعيات المشروع
```

---

## 🎨 الميزات الرئيسية

### ✅ مكتمل 100%
- **33+ صفحة** مكتملة ومتكاملة
- **نظام مصادقة** كامل
- **إدارة منتجات** متقدمة مع ذكاء اصطناعي
- **نظام طلبات** شامل
- **دفع متعدد الطرق** (نقدي، بنكي، بطاقات)
- **لوحات تحكم متخصصة** (أدمن، مدارس، طلاب، توصيل)
- **صفحات قانونية** شاملة
- **تصميم متجاوب** على جميع الأجهزة
- **دعم RTL** للغة العربية
- **وضع ليلي** محسن

### 🆕 الميزات الجديدة
- **نظام دفع بنكي متقدم** مع رفع إثبات الدفع
- **صفحات قانونية شاملة** (شروط، خصوصية، FAQ)
- **إدارة طرق الدفع** المتقدمة
- **إدارة مناطق الشحن** المحسنة

---

## 🛠️ التقنيات المستخدمة

- **Next.js 15** - إطار العمل الرئيسي
- **TypeScript** - للأمان النوعي
- **Tailwind CSS** - للتصميم المتجاوب
- **Shadcn/UI** - مكونات الواجهة
- **Lucide React** - الأيقونات
- **Supabase** - قاعدة البيانات والمصادقة

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من دليل الاختبار: `TESTING_GUIDE_NEW_FEATURES.md`
2. راجع ملخص المشروع: `PROJECT_COMPLETION_SUMMARY.md`
3. تحقق من الكونسول في المتصفح (F12)

---

## 🎉 المشروع جاهز!

جميع الصفحات المطلوبة مكتملة ومتكاملة. المشروع جاهز للاستخدام والنشر!

**استمتع بالتطوير! 🚀**
