import { NextRequest, NextResponse } from 'next/server'

// GET - إنشاء صور بديلة ديناميكية
export async function GET(
  request: NextRequest,
  { params }: { params: { params: string[] } }
) {
  try {
    const [width, height] = params.params
    const w = parseInt(width) || 400
    const h = parseInt(height) || 300
    
    // التأكد من أن الأبعاد معقولة
    const maxSize = 2000
    const finalWidth = Math.min(Math.max(w, 50), maxSize)
    const finalHeight = Math.min(Math.max(h, 50), maxSize)
    
    // إنشاء SVG بديل
    const svg = `
      <svg width="${finalWidth}" height="${finalHeight}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <rect x="10" y="10" width="${finalWidth - 20}" height="${finalHeight - 20}" 
              fill="none" stroke="#90caf9" stroke-width="2" stroke-dasharray="5,5"/>
        
        <!-- أيقونة الصورة -->
        <g transform="translate(${finalWidth/2 - 30}, ${finalHeight/2 - 30})">
          <rect x="10" y="15" width="40" height="30" fill="none" stroke="#64b5f6" stroke-width="2" rx="2"/>
          <circle cx="20" cy="25" r="3" fill="#64b5f6"/>
          <polygon points="15,35 25,25 35,30 45,20 45,40 15,40" fill="#64b5f6"/>
        </g>
        
        <!-- النص -->
        <text x="${finalWidth/2}" y="${finalHeight/2 + 50}" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="14" 
              fill="#1976d2">
          ${finalWidth} × ${finalHeight}
        </text>
        
        <!-- نص عربي -->
        <text x="${finalWidth/2}" y="${finalHeight/2 + 70}" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="12" 
              fill="#666">
          صورة تجريبية
        </text>
      </svg>
    `
    
    return new NextResponse(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    })
    
  } catch (error) {
    console.error('Error generating placeholder:', error)
    
    // إرجاع SVG بسيط في حالة الخطأ
    const errorSvg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f5f5f5"/>
        <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#999">
          خطأ في تحميل الصورة
        </text>
      </svg>
    `
    
    return new NextResponse(errorSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'no-cache',
      },
    })
  }
}
