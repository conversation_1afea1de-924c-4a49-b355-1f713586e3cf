-- إعداد سريع للمشروع (للحالات الطارئة)
-- استخدم هذا الملف إذا كنت تريد إعداد سريع دون تفاصيل كثيرة

-- إنشاء الأنواع الأساسية فقط
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('student', 'school', 'admin', 'delivery');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE menu_target_type AS ENUM ('internal', 'external', 'page');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE language_code AS ENUM ('ar', 'en', 'fr');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- إنشاء الجداول الأساسية فقط
CREATE TABLE IF NOT EXISTS menu_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title_ar TEXT NOT NULL,
    title_en TEXT,
    title_fr TEXT,
    slug TEXT UNIQUE NOT NULL,
    icon TEXT,
    parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    target_type menu_target_type NOT NULL DEFAULT 'internal',
    target_value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS pages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    slug TEXT UNIQUE NOT NULL,
    is_published BOOLEAN DEFAULT false,
    author_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    featured_image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS page_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,
    language language_code NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    meta_description TEXT,
    meta_keywords TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(page_id, language)
);

-- تفعيل RLS
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_content ENABLE ROW LEVEL SECURITY;

-- سياسات أساسية
CREATE POLICY "Anyone can view active menu items" ON menu_items
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage menu items" ON menu_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Anyone can view published pages" ON pages
    FOR SELECT USING (is_published = true);

CREATE POLICY "Admins can manage pages" ON pages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Anyone can view published page content" ON page_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM pages 
            WHERE id = page_id AND is_published = true
        )
    );

CREATE POLICY "Admins can manage page content" ON page_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers
CREATE TRIGGER update_menu_items_updated_at
    BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pages_updated_at
    BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_page_content_updated_at
    BEFORE UPDATE ON page_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج عناصر القائمة الأساسية
INSERT INTO menu_items (title_ar, title_en, title_fr, slug, icon, order_index, target_type, target_value) VALUES
('الرئيسية', 'Home', 'Accueil', 'home', 'Home', 1, 'internal', '/'),
('الكتالوج', 'Catalog', 'Catalogue', 'catalog', 'ShoppingBag', 2, 'internal', '/catalog'),
('من نحن', 'About', 'À Propos', 'about', 'Info', 3, 'internal', '/about'),
('تواصل معنا', 'Contact', 'Contact', 'contact', 'Phone', 4, 'internal', '/contact')
ON CONFLICT (slug) DO NOTHING;

-- إنشاء مدير افتراضي
DO $$ 
DECLARE
    admin_user_id uuid;
BEGIN
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        admin_user_id := gen_random_uuid();
        
        INSERT INTO profiles (
            id,
            email,
            full_name,
            role,
            phone,
            created_at,
            updated_at
        ) VALUES (
            admin_user_id,
            '<EMAIL>',
            'مدير الموقع الرئيسي',
            'admin',
            '+212 5XX-XXXXXX',
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'تم إنشاء ملف تعريفي للمدير: <EMAIL>';
    ELSE
        RAISE NOTICE 'المدير موجود بالفعل';
    END IF;
END $$;

-- عرض النتيجة
SELECT 
    'الإعداد السريع مكتمل!' as status,
    (SELECT COUNT(*) FROM menu_items) as menu_items,
    (SELECT COUNT(*) FROM profiles WHERE role = 'admin') as admins;

SELECT 
    'الخطوات التالية:' as next,
    '1. أنشئ مستخدم: <EMAIL>' as step1,
    '2. شغل setup-admin.sql' as step2;
