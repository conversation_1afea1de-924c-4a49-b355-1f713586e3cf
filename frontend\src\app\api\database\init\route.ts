import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, checkDatabaseHealth } from '@/lib/database'

// POST - تهيئة قاعدة البيانات
export async function POST(request: NextRequest) {
  try {
    console.log('بدء تهيئة قاعدة البيانات...')
    
    // تهيئة قاعدة البيانات
    await initializeDatabase()
    
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    
    if (isHealthy) {
      return NextResponse.json({
        message: 'تم تهيئة قاعدة البيانات بنجاح',
        status: 'success',
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        error: 'فشل في التحقق من حالة قاعدة البيانات بعد التهيئة',
        status: 'error'
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error initializing database:', error)
    return NextResponse.json({
      error: 'فشل في تهيئة قاعدة البيانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      status: 'error'
    }, { status: 500 })
  }
}

// GET - التحقق من حالة قاعدة البيانات
export async function GET(request: NextRequest) {
  try {
    const isHealthy = await checkDatabaseHealth()
    
    if (isHealthy) {
      return NextResponse.json({
        status: 'healthy',
        message: 'قاعدة البيانات تعمل بشكل طبيعي',
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        status: 'unhealthy',
        message: 'قاعدة البيانات غير متاحة',
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error('Error checking database health:', error)
    return NextResponse.json({
      status: 'error',
      message: 'فشل في فحص حالة قاعدة البيانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
