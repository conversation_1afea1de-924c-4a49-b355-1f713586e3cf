"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { PageLayout } from '@/components/layouts/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CreditCard,
  Copy,
  CheckCircle,
  Upload,
  FileImage,
  AlertTriangle,
  Clock,
  ArrowLeft,
  QrCode,
  Building,
  User,
  Hash,
  Calendar,
  DollarSign
} from 'lucide-react'
import { toast } from 'sonner'

interface BankAccount {
  bank_name: string
  account_holder: string
  account_number: string
  rib: string
  swift?: string
  branch?: string
}

interface OrderSummary {
  order_id: string
  total_amount: number
  items: Array<{
    name: string
    quantity: number
    price: number
  }>
  shipping_cost: number
  tax_amount: number
}

const bankAccounts: BankAccount[] = [
  {
    bank_name: 'البنك الشعبي المغربي',
    account_holder: 'منصة أزياء التخرج المغربية',
    account_number: '****************',
    rib: '236 000 ********** 12',
    swift: 'BMCEMAMC',
    branch: 'بني ملال'
  },
  {
    bank_name: 'بنك المغرب',
    account_holder: 'منصة أزياء التخرج المغربية', 
    account_number: '011000**********',
    rib: '011 000 ********** 34',
    swift: 'BMCEMAMC',
    branch: 'خنيفرة'
  }
]

export default function BankTransferPage() {
  const searchParams = useSearchParams()
  const [selectedBank, setSelectedBank] = useState(0)
  const [proofFile, setProofFile] = useState<File | null>(null)
  const [transferReference, setTransferReference] = useState('')
  const [transferDate, setTransferDate] = useState('')
  const [notes, setNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [orderSummary, setOrderSummary] = useState<OrderSummary | null>(null)

  // جلب معلومات الطلب من URL parameters أو localStorage
  useEffect(() => {
    const orderId = searchParams.get('order_id')
    const amount = searchParams.get('amount')
    
    if (orderId && amount) {
      // في التطبيق الحقيقي، ستجلب هذه البيانات من API
      setOrderSummary({
        order_id: orderId,
        total_amount: parseFloat(amount),
        items: [
          { name: 'ثوب التخرج الكلاسيكي', quantity: 1, price: 250 },
          { name: 'قبعة التخرج', quantity: 1, price: 80 }
        ],
        shipping_cost: 50,
        tax_amount: 66
      })
    } else {
      // بيانات تجريبية
      setOrderSummary({
        order_id: 'ORD-2024-001',
        total_amount: 396,
        items: [
          { name: 'ثوب التخرج الكلاسيكي', quantity: 1, price: 250 },
          { name: 'قبعة التخرج', quantity: 1, price: 80 }
        ],
        shipping_cost: 50,
        tax_amount: 66
      })
    }
  }, [searchParams])

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`تم نسخ ${label} بنجاح`)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('حجم الملف يجب أن يكون أقل من 5 ميجابايت')
        return
      }
      if (!file.type.startsWith('image/')) {
        toast.error('يرجى اختيار ملف صورة صالح')
        return
      }
      setProofFile(file)
      toast.success('تم اختيار الملف بنجاح')
    }
  }

  const handleSubmitProof = async () => {
    if (!proofFile || !transferReference || !transferDate) {
      toast.error('يرجى ملء جميع الحقول المطلوبة ورفع إثبات الدفع')
      return
    }

    setIsSubmitting(true)
    
    try {
      // محاكاة رفع الملف وحفظ البيانات
      const formData = new FormData()
      formData.append('proof_file', proofFile)
      formData.append('order_id', orderSummary?.order_id || '')
      formData.append('transfer_reference', transferReference)
      formData.append('transfer_date', transferDate)
      formData.append('bank_account', JSON.stringify(bankAccounts[selectedBank]))
      formData.append('notes', notes)

      // في التطبيق الحقيقي، ستقوم بإرسال البيانات إلى API
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('تم إرسال إثبات الدفع بنجاح')
      
      // توجيه إلى صفحة تأكيد الدفع
      window.location.href = `/payment/confirmation?order_id=${orderSummary?.order_id}&method=bank_transfer`
      
    } catch (error) {
      toast.error('حدث خطأ أثناء إرسال إثبات الدفع')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!orderSummary) {
    return (
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            معلومات الطلب غير متوفرة
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
            يرجى العودة إلى صفحة إتمام الطلب
          </p>
          <Button asChild>
            <a href="/checkout" className="arabic-text">
              العودة لإتمام الطلب
            </a>
          </Button>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button variant="outline" size="sm" asChild className="mb-4">
          <a href="/checkout">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة لإتمام الطلب
          </a>
        </Button>
        
        <div className="text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <CreditCard className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
              الدفع عبر التحويل البنكي
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300 arabic-text">
            اتبع الخطوات التالية لإتمام عملية الدفع
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto grid lg:grid-cols-3 gap-8">
        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle className="arabic-text">ملخص الطلب</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">رقم الطلب:</span>
                  <Badge variant="outline">{orderSummary.order_id}</Badge>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                {orderSummary.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium arabic-text">{item.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        الكمية: {item.quantity}
                      </p>
                    </div>
                    <span className="font-medium">{item.price} Dhs</span>
                  </div>
                ))}
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="arabic-text">المجموع الفرعي:</span>
                  <span>{orderSummary.items.reduce((sum, item) => sum + item.price, 0)} Dhs</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">الشحن:</span>
                  <span>{orderSummary.shipping_cost} Dhs</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">الضريبة:</span>
                  <span>{orderSummary.tax_amount} Dhs</span>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center text-lg font-bold">
                <span className="arabic-text">المجموع الكلي:</span>
                <span className="text-blue-600">{orderSummary.total_amount} Dhs</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Instructions */}
        <div className="lg:col-span-2 space-y-6">
          {/* Step 1: Choose Bank Account */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
                اختر الحساب البنكي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {bankAccounts.map((account, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedBank === index 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedBank(index)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full border-2 ${
                        selectedBank === index 
                          ? 'border-blue-500 bg-blue-500' 
                          : 'border-gray-300'
                      }`}>
                        {selectedBank === index && (
                          <CheckCircle className="w-4 h-4 text-white" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Building className="h-4 w-4 text-blue-600" />
                          <span className="font-medium arabic-text">{account.bank_name}</span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          <p>الفرع: {account.branch}</p>
                          <p>صاحب الحساب: {account.account_holder}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Step 2: Bank Account Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
                تفاصيل الحساب البنكي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 arabic-text">
                      <Building className="h-4 w-4" />
                      اسم البنك
                    </Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={bankAccounts[selectedBank].bank_name} 
                        readOnly 
                        className="bg-white dark:bg-gray-700"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(bankAccounts[selectedBank].bank_name, 'اسم البنك')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 arabic-text">
                      <User className="h-4 w-4" />
                      صاحب الحساب
                    </Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={bankAccounts[selectedBank].account_holder} 
                        readOnly 
                        className="bg-white dark:bg-gray-700"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(bankAccounts[selectedBank].account_holder, 'صاحب الحساب')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 arabic-text">
                      <Hash className="h-4 w-4" />
                      رقم الحساب
                    </Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={bankAccounts[selectedBank].account_number} 
                        readOnly 
                        className="bg-white dark:bg-gray-700 font-mono"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(bankAccounts[selectedBank].account_number, 'رقم الحساب')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2 arabic-text">
                      <QrCode className="h-4 w-4" />
                      RIB
                    </Label>
                    <div className="flex items-center gap-2">
                      <Input 
                        value={bankAccounts[selectedBank].rib} 
                        readOnly 
                        className="bg-white dark:bg-gray-700 font-mono"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(bankAccounts[selectedBank].rib, 'RIB')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <DollarSign className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-100 arabic-text">
                        المبلغ المطلوب تحويله
                      </h4>
                      <p className="text-2xl font-bold text-blue-600 mt-1">
                        {orderSummary.total_amount} درهم مغربي
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1 arabic-text">
                        يرجى تحويل المبلغ بالضبط مع ذكر رقم الطلب: {orderSummary.order_id}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: Upload Proof */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
                رفع إثبات الدفع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="transfer_reference" className="arabic-text">
                    رقم العملية / المرجع *
                  </Label>
                  <Input
                    id="transfer_reference"
                    value={transferReference}
                    onChange={(e) => setTransferReference(e.target.value)}
                    placeholder="أدخل رقم العملية"
                    className="arabic-text"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="transfer_date" className="arabic-text">
                    تاريخ التحويل *
                  </Label>
                  <Input
                    id="transfer_date"
                    type="date"
                    value={transferDate}
                    onChange={(e) => setTransferDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="proof_file" className="arabic-text">
                  صورة إثبات الدفع *
                </Label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  {proofFile ? (
                    <div className="space-y-2">
                      <FileImage className="h-8 w-8 text-green-600 mx-auto" />
                      <p className="text-sm font-medium text-green-600">{proofFile.name}</p>
                      <p className="text-xs text-gray-500">
                        {(proofFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setProofFile(null)}
                      >
                        إزالة الملف
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                      <div>
                        <Label htmlFor="proof_file" className="cursor-pointer">
                          <span className="text-blue-600 hover:text-blue-500">اختر ملف</span>
                          <span className="text-gray-500"> أو اسحب الملف هنا</span>
                        </Label>
                        <Input
                          id="proof_file"
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, JPEG حتى 5MB
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes" className="arabic-text">
                  ملاحظات إضافية
                </Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="أي ملاحظات إضافية حول التحويل..."
                  className="arabic-text"
                  rows={3}
                />
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-900 dark:text-yellow-100 arabic-text">
                      مهم: أوقات المعالجة
                    </h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1 arabic-text">
                      سيتم مراجعة إثبات الدفع خلال 2-4 ساعات عمل. ستتلقون تأكيداً عبر البريد الإلكتروني عند الموافقة.
                    </p>
                  </div>
                </div>
              </div>

              <Button 
                onClick={handleSubmitProof}
                disabled={!proofFile || !transferReference || !transferDate || isSubmitting}
                className="w-full"
                size="lg"
              >
                {isSubmitting ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    جاري الإرسال...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    إرسال إثبات الدفع
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  )
}
