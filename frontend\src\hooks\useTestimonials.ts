"use client"

import { useState, useEffect } from 'react'

export interface Testimonial {
  id: string
  name: string
  role: string
  university: string
  content: string
  rating: number
  avatar: string
  date: string
  verified?: boolean
}

interface UseTestimonialsReturn {
  testimonials: Testimonial[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useTestimonials(): UseTestimonialsReturn {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mockTestimonials: Testimonial[] = [
    {
      id: '1',
      name: 'أمينة الحسني',
      role: 'طالبة ماجستير',
      university: 'جامعة محمد الخامس',
      content: 'تجربة رائعة! الثوب كان مثالياً وجودة القماش عالية جداً. الخدمة كانت احترافية والتسليم في الوقت المحدد.',
      rating: 5,
      avatar: '/avatars/amina.jpg',
      date: 'منذ أسبوعين',
      verified: true
    },
    {
      id: '2',
      name: 'يوسف بنعلي',
      role: 'طالب دكتوراه',
      university: 'جامعة الحسن الثاني',
      content: 'المنصة سهلة الاستخدام والتخصيص كان ممتازاً. حصلت على ثوب التخرج المثالي بفضل المساعد الذكي.',
      rating: 5,
      avatar: '/avatars/youssef.jpg',
      date: 'منذ 3 أسابيع',
      verified: true
    },
    {
      id: '3',
      name: 'فاطمة الزهراء',
      role: 'طالبة بكالوريوس',
      university: 'جامعة القاضي عياض',
      content: 'أعجبني التنوع في التصاميم والألوان. الفريق كان متعاوناً جداً وساعدني في اختيار الأفضل.',
      rating: 5,
      avatar: '/avatars/fatima.jpg',
      date: 'منذ شهر',
      verified: true
    },
    {
      id: '4',
      name: 'عبد الرحمن التازي',
      role: 'طالب هندسة',
      university: 'المدرسة المحمدية للمهندسين',
      content: 'جودة عالية وأسعار معقولة. التتبع كان دقيقاً والتسليم سريع. أنصح بها بشدة!',
      rating: 5,
      avatar: '/avatars/abderrahman.jpg',
      date: 'منذ شهر ونصف',
      verified: true
    },
    {
      id: '5',
      name: 'خديجة المرابط',
      role: 'طالبة طب',
      university: 'جامعة سيدي محمد بن عبد الله',
      content: 'تجربة استثنائية من البداية للنهاية. الثوب كان أنيقاً ومريحاً، والخدمة كانت ممتازة.',
      rating: 5,
      avatar: '/avatars/khadija.jpg',
      date: 'منذ شهرين',
      verified: true
    },
    {
      id: '6',
      name: 'محمد الإدريسي',
      role: 'طالب إدارة أعمال',
      university: 'جامعة ابن طفيل',
      content: 'المنصة احترافية والذكاء الاصطناعي ساعدني كثيراً في الاختيار. النتيجة كانت فوق التوقعات.',
      rating: 5,
      avatar: '/avatars/mohammed.jpg',
      date: 'منذ شهرين ونصف',
      verified: true
    },
    {
      id: '7',
      name: 'سارة العلوي',
      role: 'طالبة صيدلة',
      university: 'جامعة الرباط',
      content: 'خدمة عملاء ممتازة وتصاميم رائعة. أحببت إمكانية التخصيص والمعاينة المسبقة.',
      rating: 4,
      avatar: '/avatars/sara.jpg',
      date: 'منذ 3 أشهر',
      verified: true
    },
    {
      id: '8',
      name: 'أحمد الفاسي',
      role: 'طالب قانون',
      university: 'جامعة فاس',
      content: 'تجربة مميزة وسعر مناسب. الثوب وصل في الوقت المحدد وكان بالجودة المطلوبة.',
      rating: 5,
      avatar: '/avatars/ahmed.jpg',
      date: 'منذ 3 أشهر ونصف',
      verified: true
    }
  ]

  const fetchTestimonials = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1200))
      
      // Mock data - replace with actual API response
      setTestimonials(mockTestimonials)
    } catch (err) {
      setError('فشل في تحميل آراء العملاء')
      console.error('Error fetching testimonials:', err)
    } finally {
      setLoading(false)
    }
  }

  const refetch = () => {
    fetchTestimonials()
  }

  useEffect(() => {
    fetchTestimonials()
  }, [])

  return {
    testimonials,
    loading,
    error,
    refetch
  }
}
