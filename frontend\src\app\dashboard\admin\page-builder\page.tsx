'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole } from '@/types/auth'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PageBuilder } from '@/components/admin/PageBuilder'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AdminHeader } from '@/components/admin/AdminHeader'
import { ArrowLeft, Sparkles, FileText, Upload, Zap, Wand2, Layout, Edit, Trash2, Eye, Globe, Calendar, User } from 'lucide-react'
import Link from 'next/link'
import { PageProject } from '@/types/page-builder'
import { MockDataManager } from '@/lib/mockData'
import { toast } from 'sonner'

export default function PageBuilderPage() {
  const { user, profile } = useAuth()
  const [projects, setProjects] = useState<PageProject[]>([])
  const [loading, setLoading] = useState(true)
  const [showBuilder, setShowBuilder] = useState(false)
  const [currentProject, setCurrentProject] = useState<PageProject | undefined>()
  const [stats, setStats] = useState<any>({})

  // جلب المشاريع
  const fetchProjects = async () => {
    try {
      setLoading(true)
      // جلب المشاريع من localStorage
      const savedProjects = MockDataManager.getPageProjects()
      setProjects(savedProjects)
      
      // حساب الإحصائيات
      const totalProjects = savedProjects.length
      const publishedProjects = savedProjects.filter((p: PageProject) => p.isPublished).length
      const activeProjects = savedProjects.filter((p: PageProject) => !p.isPublished).length
      const generatedPages = savedProjects.filter((p: PageProject) => p.generationMode === 'ai').length
      
      setStats({
        totalProjects,
        publishedProjects,
        activeProjects,
        generatedPages
      })
    } catch (error) {
      console.error('Error fetching projects:', error)
      toast.error('خطأ في جلب المشاريع')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [])

  // حفظ مشروع
  const saveProject = async (project: PageProject) => {
    try {
      toast.success('تم حفظ المشروع بنجاح')
      fetchProjects()
    } catch (error) {
      toast.error('فشل في حفظ المشروع')
    }
  }



  // نشر مشروع
  const publishProject = async (project: PageProject) => {
    try {
      const projects = MockDataManager.getPageProjects()
      const projectIndex = projects.findIndex(p => p.id === project.id)
      if (projectIndex !== -1) {
        projects[projectIndex].isPublished = !projects[projectIndex].isPublished
        projects[projectIndex].updatedAt = new Date().toISOString()
        MockDataManager.savePageProjects(projects)
        fetchProjects()
        toast.success(projects[projectIndex].isPublished ? 'تم نشر المشروع بنجاح' : 'تم إلغاء نشر المشروع')
      }
    } catch (error) {
      toast.error('فشل في تحديث حالة النشر')
    }
  }

  // حذف مشروع
  const deleteProject = async (projectId: string) => {
    try {
      const projects = MockDataManager.getPageProjects()
      const filteredProjects = projects.filter(p => p.id !== projectId)
      MockDataManager.savePageProjects(filteredProjects)
      fetchProjects()
      toast.success('تم حذف المشروع بنجاح')
    } catch (error) {
      toast.error('فشل في حذف المشروع')
    }
  }

  // تحرير مشروع
  const editProject = (project: PageProject) => {
    setCurrentProject(project)
    setShowBuilder(true)
  }

  // معاينة مشروع
  const handlePreview = (project: PageProject) => {
    // يمكن إضافة نافذة معاينة أو توجيه لصفحة معاينة
    toast.info('ميزة المعاينة قيد التطوير')
  }

  if (showBuilder) {
    return (
      <ProtectedRoute requiredRole={UserRole.ADMIN}>
        <div className="h-screen flex flex-col">
          <div className="border-b p-4 bg-background">
            <Button 
              variant="outline" 
              onClick={() => setShowBuilder(false)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للمشاريع
            </Button>
          </div>
          <div className="flex-1">
            <PageBuilder
              project={currentProject}
              onSave={saveProject}
              onPreview={handlePreview}
              onPublish={publishProject}
            />
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <AdminHeader
          title="بناء الصفحات الذكية ✨"
          subtitle="إنشاء صفحات احترافية باستخدام الذكاء الاصطناعي والقوالب الجاهزة"
        />
        
        <main className="container mx-auto px-4 py-8">

          {/* الإجراءات السريعة */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white arabic-text mb-4">
              الإجراءات السريعة
            </h2>
            <div className="flex flex-wrap gap-3">
              <Button onClick={() => setShowBuilder(true)} className="flex items-center gap-2">
                <Wand2 className="h-4 w-4" />
                توليد بالذكاء الاصطناعي
              </Button>
              <Button variant="outline" onClick={() => setShowBuilder(true)} className="flex items-center gap-2">
                <Layout className="h-4 w-4" />
                اختيار قالب
              </Button>
              <Button variant="outline" onClick={fetchProjects} className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                تحديث المشاريع
              </Button>
            </div>
          </div>

          {/* الإحصائيات السريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">إجمالي المشاريع</p>
                    <p className="text-2xl font-bold">{stats.totalProjects || 0}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">المشاريع المنشورة</p>
                    <p className="text-2xl font-bold">{stats.publishedProjects || 0}</p>
                  </div>
                  <Upload className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">المشاريع النشطة</p>
                    <p className="text-2xl font-bold">{stats.activeProjects || 0}</p>
                  </div>
                  <Zap className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">الصفحات المولدة</p>
                    <p className="text-2xl font-bold">{stats.generatedPages || 0}</p>
                  </div>
                  <Sparkles className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أزرار الإجراءات الرئيسية */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex gap-3">
              <Button onClick={() => setShowBuilder(true)} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Wand2 className="h-4 w-4 mr-2" />
                إنشاء صفحة جديدة
              </Button>
              <Button variant="outline" onClick={() => setShowBuilder(true)}>
                <Layout className="h-4 w-4 mr-2" />
                استخدام قالب
              </Button>
            </div>
          </div>

          {/* محتوى الصفحة */}
          {projects.length === 0 ? (
            <div className="text-center py-12">
              <Sparkles className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-xl font-semibold mb-2">مرحباً ببناء الصفحات الذكية</h3>
              <p className="text-muted-foreground mb-6">
                ابدأ بإنشاء صفحة جديدة باستخدام الذكاء الاصطناعي أو اختر قالباً جاهزاً
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => setShowBuilder(true)}>
                  <Wand2 className="h-4 w-4 mr-2" />
                  توليد بالذكاء الاصطناعي
                </Button>
                <Button variant="outline" onClick={() => setShowBuilder(true)}>
                  <Layout className="h-4 w-4 mr-2" />
                  اختيار قالب
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white arabic-text">
                المشاريع المحفوظة
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects.map((project) => (
                  <Card key={project.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg arabic-text line-clamp-1">
                            {project.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant={project.isPublished ? "default" : "secondary"}>
                              {project.isPublished ? (
                                <>
                                  <Globe className="h-3 w-3 mr-1" />
                                  منشور
                                </>
                              ) : (
                                <>
                                  <FileText className="h-3 w-3 mr-1" />
                                  مسودة
                                </>
                              )}
                            </Badge>
                            <Badge variant="outline">
                              {project.generationMode === 'ai' ? (
                                <>
                                  <Sparkles className="h-3 w-3 mr-1" />
                                  ذكاء اصطناعي
                                </>
                              ) : (
                                <>
                                  <Layout className="h-3 w-3 mr-1" />
                                  قالب
                                </>
                              )}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <p className="text-sm text-muted-foreground arabic-text line-clamp-2 mb-4">
                        {project.description}
                      </p>

                      <div className="flex items-center gap-2 text-xs text-muted-foreground mb-4">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(project.createdAt).toLocaleDateString('ar-MA')}</span>
                        <span>•</span>
                        <span>{project.components.length} مكون</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => editProject(project)}
                          className="flex-1"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          تحرير
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePreview(project)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant={project.isPublished ? "default" : "outline"}
                          onClick={() => publishProject(project)}
                        >
                          <Globe className="h-3 w-3" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => deleteProject(project.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  )
}
