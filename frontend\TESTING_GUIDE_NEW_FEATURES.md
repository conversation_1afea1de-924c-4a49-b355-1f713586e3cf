# 🧪 دليل اختبار الميزات الجديدة - منصة أزياء التخرج المغربية

## 🚀 تشغيل المشروع

```bash
cd frontend
npm install
npm run dev
```

الموقع سيكون متاح على: http://localhost:3000

---

## 📋 الصفحات الجديدة للاختبار

### 1. 📄 الصفحات القانونية والمساعدة

#### أ) صفحة الشروط والأحكام
- **الرابط**: http://localhost:3000/terms-conditions
- **ما يجب اختباره**:
  - ✅ تحميل الصفحة بدون أخطاء
  - ✅ عرض المحتوى باللغة العربية بشكل صحيح
  - ✅ تصميم متجاوب على الهاتف والكمبيوتر
  - ✅ ظهور الأيقونات والألوان بشكل صحيح
  - ✅ عمل Navigation و Footer
  - ✅ الوضع الليلي يعمل بشكل صحيح

#### ب) صفحة سياسة الخصوصية
- **الرابط**: http://localhost:3000/privacy-policy
- **ما يجب اختباره**:
  - ✅ تحميل الصفحة بدون أخطاء
  - ✅ عرض أقسام حماية البيانات
  - ✅ أيقونات الأمان والخصوصية
  - ✅ معلومات التواصل في النهاية
  - ✅ تصميم Cards منظم

#### ج) صفحة الأسئلة الشائعة
- **الرابط**: http://localhost:3000/faq
- **ما يجب اختباره**:
  - ✅ تحميل الصفحة مع 14 سؤال وجواب
  - ✅ عمل البحث في الأسئلة
  - ✅ فلترة حسب الفئات (عام، طلبات، دفع، توصيل، إلخ)
  - ✅ توسيع وطي الأسئلة بالنقر
  - ✅ عرض معلومات التواصل في النهاية
  - ✅ تصميم تفاعلي ومتجاوب

### 2. 💳 نظام الدفع البنكي

#### أ) صفحة الدفع البنكي
- **الرابط**: http://localhost:3000/payment/bank-transfer
- **ما يجب اختباره**:
  - ✅ تحميل الصفحة مع بيانات الطلب
  - ✅ عرض الحسابات البنكية (البنك الشعبي وبنك المغرب)
  - ✅ إمكانية نسخ بيانات الحساب (Copy buttons)
  - ✅ رفع ملف إثبات الدفع
  - ✅ ملء نموذج تفاصيل التحويل
  - ✅ التحقق من صحة البيانات قبل الإرسال
  - ✅ رسائل التنبيه (Toast notifications)

#### ب) صفحة تأكيد الدفع
- **الرابط**: http://localhost:3000/payment/confirmation
- **ما يجب اختباره**:
  - ✅ عرض حالات مختلفة (مؤكد، معلق، فشل)
  - ✅ تفاصيل الطلب والدفع
  - ✅ معلومات التوصيل
  - ✅ أزرار الإجراءات (تحميل الإيصال، تتبع الطلب)
  - ✅ معلومات الدعم في حالة الفشل

### 3. 🎛️ الصفحات الإدارية الجديدة

#### أ) إدارة طرق الدفع
- **الرابط**: http://localhost:3000/dashboard/admin/payment-methods
- **ما يجب اختباره**:
  - ✅ تحميل الصفحة (يتطلب تسجيل دخول كأدمن)
  - ✅ عرض طرق الدفع الثلاث (نقدي، بنكي، بطاقات)
  - ✅ تفعيل/تعطيل طرق الدفع
  - ✅ إدارة الحسابات البنكية
  - ✅ الإعدادات العامة للدفع
  - ✅ حفظ التغييرات

#### ب) إدارة التوصيل المحسنة
- **الرابط**: http://localhost:3000/dashboard/admin/delivery
- **ما يجب اختباره**:
  - ✅ التبويب الجديد "المناطق والشحن"
  - ✅ إدارة مناطق التوصيل (بني ملال، خنيفرة، إلخ)
  - ✅ تحديد أسعار الشحن لكل منطقة
  - ✅ إعدادات الشحن العامة
  - ✅ إحصائيات الشحن

---

## 🔗 اختبار التكامل

### 1. Footer المحدث
- **اختبر**: الروابط الجديدة في Footer
- **يجب أن تعمل**:
  - رابط "الأسئلة الشائعة" → `/faq`
  - رابط "الشروط والأحكام" → `/terms-conditions`
  - رابط "سياسة الخصوصية" → `/privacy-policy`

### 2. لوحة التحكم الإدارية
- **اختبر**: الروابط الجديدة في `/dashboard/admin`
- **يجب أن تعمل**:
  - رابط "إدارة طرق الدفع" → `/dashboard/admin/payment-methods`
  - رابط "إدارة التوصيل" → `/dashboard/admin/delivery`
  - رابط "الإعدادات العامة" → `/dashboard/admin/settings`

### 3. تدفق الدفع البنكي
- **اختبر**: من صفحة إتمام الطلب
- **الخطوات**:
  1. اذهب إلى `/checkout`
  2. اختر "تحويل بنكي" كطريقة دفع
  3. أكمل الطلب
  4. يجب التوجيه إلى `/payment/bank-transfer`
  5. أكمل عملية الدفع
  6. يجب التوجيه إلى `/payment/confirmation`

---

## 📱 اختبار التصميم المتجاوب

### الأجهزة للاختبار:
- **الهاتف المحمول**: 320px - 768px
- **الجهاز اللوحي**: 768px - 1024px
- **سطح المكتب**: 1024px+

### نقاط التحقق:
- ✅ النصوص العربية تظهر بشكل صحيح (RTL)
- ✅ الأيقونات في المكان الصحيح
- ✅ الأزرار قابلة للنقر بسهولة
- ✅ النماذج سهلة الاستخدام
- ✅ الجداول تتمرر أفقياً عند الحاجة

---

## 🌙 اختبار الوضع الليلي

### كيفية الاختبار:
1. انقر على أيقونة القمر/الشمس في Navigation
2. تحقق من تغيير الألوان في جميع الصفحات
3. تأكد من وضوح النصوص والأيقونات

### الصفحات للاختبار:
- جميع الصفحات الجديدة
- لوحة التحكم الإدارية
- النماذج والجداول

---

## ⚠️ الأخطاء المحتملة وحلولها

### 1. خطأ "CreditCard is not defined"
- **الحل**: تم إصلاحه بإضافة الاستيراد المناسب

### 2. خطأ "PageLayout not found"
- **الحل**: تم إصلاحه بتصحيح مسارات الاستيراد

### 3. صفحة فارغة أو خطأ 404
- **تحقق من**: صحة الروابط والمسارات
- **تأكد من**: تشغيل الخادم على المنفذ الصحيح

### 4. مشاكل في التصميم
- **تحقق من**: تحميل Tailwind CSS بشكل صحيح
- **تأكد من**: عدم وجود تعارض في الأنماط

---

## 📊 تقرير الاختبار

### قائمة التحقق:
- [ ] جميع الصفحات تحمل بدون أخطاء
- [ ] النصوص العربية تظهر بشكل صحيح
- [ ] التصميم متجاوب على جميع الأجهزة
- [ ] الوضع الليلي يعمل بشكل صحيح
- [ ] النماذج تعمل وترسل البيانات
- [ ] الروابط تعمل بشكل صحيح
- [ ] الأيقونات تظهر بشكل صحيح
- [ ] رسائل التنبيه تعمل

---

## 🎯 النتيجة المتوقعة

بعد اجتياز جميع الاختبارات، يجب أن يكون لديك:
- ✅ **5 صفحات جديدة** تعمل بشكل مثالي
- ✅ **نظام دفع بنكي متكامل**
- ✅ **صفحات قانونية شاملة**
- ✅ **إدارة متقدمة للدفع والشحن**
- ✅ **تصميم احترافي ومتجاوب**

**المشروع جاهز للاستخدام! 🎉**
