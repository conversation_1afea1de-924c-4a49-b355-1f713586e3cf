import { NextRequest, NextResponse } from 'next/server'
import { ProductModel } from '@/lib/models/Product'
import { checkDatabaseHealth, initializeDatabase } from '@/lib/database'

// البيانات الأولية للمنتجات
const seedProducts = [
  {
    name: 'ثوب التخرج الكلاسيكي',
    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',
    price: 299.99,
    rental_price: 99.99,
    colors: ['أسود', 'أزرق داكن', 'بني'],
    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],
    stock_quantity: 25,
    is_available: true,
    is_published: true,
    features: ['مقاوم للتجاعيد', 'قابل للغسل', 'خامة عالية الجودة', 'تصميم كلاسيكي'],
    specifications: {
      material: 'بوليستر عالي الجودة',
      weight: '400 جرام',
      care: 'غسيل جاف أو غسيل عادي',
      origin: 'صنع في المغرب'
    }
  },
  {
    name: 'قبعة التخرج التقليدية',
    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',
    price: 79.99,
    rental_price: 29.99,
    colors: ['أسود', 'أزرق'],
    sizes: ['S', 'M', 'L'],
    images: ['/images/products/cap-traditional-1.jpg'],
    stock_quantity: 50,
    is_available: true,
    is_published: true,
    features: ['شرابة ذهبية', 'قابل للتعديل', 'خفيف الوزن'],
    specifications: {
      material: 'قطن مخلوط',
      tassel: 'خيط ذهبي',
      adjustable: 'نعم'
    }
  },
  {
    name: 'وشاح التخرج المطرز',
    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',
    price: 149.99,
    rental_price: 49.99,
    colors: ['ذهبي', 'فضي', 'أزرق'],
    sizes: ['واحد'],
    images: ['/images/products/sash-embroidered-1.jpg'],
    stock_quantity: 30,
    is_available: true,
    is_published: true,
    features: ['تطريز ذهبي', 'حرير طبيعي', 'تصميم أنيق'],
    specifications: {
      material: 'حرير طبيعي',
      embroidery: 'خيط ذهبي',
      length: '150 سم'
    }
  },
  {
    name: 'شرابة التخرج الذهبية',
    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',
    price: 39.99,
    rental_price: 15.99,
    colors: ['ذهبي', 'فضي'],
    sizes: ['واحد'],
    images: ['/images/products/tassel-gold-1.jpg'],
    stock_quantity: 100,
    is_available: true,
    is_published: true,
    features: ['خيط ذهبي', 'تصميم تقليدي', 'جودة عالية'],
    specifications: {
      material: 'خيط ذهبي',
      length: '20 سم',
      weight: '50 جرام'
    }
  },
  {
    name: 'قلنسوة الدكتوراه الفاخرة',
    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',
    price: 199.99,
    rental_price: 79.99,
    colors: ['أسود', 'أزرق داكن'],
    sizes: ['S', 'M', 'L'],
    images: ['/images/products/doctoral-cap-1.jpg'],
    stock_quantity: 15,
    is_available: true,
    is_published: true,
    features: ['تصميم فاخر', 'خامة مميزة', 'للدكتوراه فقط'],
    specifications: {
      material: 'مخمل فاخر',
      degree: 'دكتوراه',
      style: 'تقليدي'
    }
  }
]

// POST - إضافة البيانات الأولية
export async function POST(request: NextRequest) {
  try {
    console.log('بدء إضافة البيانات الأولية...')

    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      console.log('قاعدة البيانات غير متاحة، محاولة التهيئة...')
      await initializeDatabase()
    }

    // التحقق من وجود منتجات مسبقاً
    const existingProducts = await ProductModel.getAll({ limit: 1 })
    if (existingProducts.total > 0) {
      return NextResponse.json({
        message: 'البيانات الأولية موجودة بالفعل',
        existingCount: existingProducts.total,
        status: 'already_seeded'
      })
    }

    // إضافة المنتجات
    const createdProducts = []
    for (const productData of seedProducts) {
      try {
        const product = await ProductModel.create(productData)
        createdProducts.push(product)
        console.log(`تم إنشاء المنتج: ${product.name}`)
      } catch (error) {
        console.error(`فشل في إنشاء المنتج: ${productData.name}`, error)
      }
    }

    console.log(`تم إنشاء ${createdProducts.length} منتج بنجاح`)

    return NextResponse.json({
      message: 'تم إضافة البيانات الأولية بنجاح',
      createdCount: createdProducts.length,
      products: createdProducts.map(p => ({
        id: p.id,
        name: p.name,
        price: p.price
      })),
      status: 'success'
    })
  } catch (error) {
    console.error('خطأ في إضافة البيانات الأولية:', error)
    return NextResponse.json({
      error: 'فشل في إضافة البيانات الأولية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      status: 'error'
    }, { status: 500 })
  }
}

// GET - التحقق من حالة البيانات الأولية
export async function GET(request: NextRequest) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json({
        status: 'database_unavailable',
        message: 'قاعدة البيانات غير متاحة'
      }, { status: 503 })
    }

    // جلب عدد المنتجات الموجودة
    const result = await ProductModel.getAll({ limit: 1 })
    
    return NextResponse.json({
      status: result.total > 0 ? 'seeded' : 'not_seeded',
      totalProducts: result.total,
      message: result.total > 0 
        ? `يوجد ${result.total} منتج في قاعدة البيانات`
        : 'لا توجد منتجات في قاعدة البيانات',
      seedDataAvailable: seedProducts.length,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('خطأ في فحص البيانات الأولية:', error)
    return NextResponse.json({
      status: 'error',
      message: 'فشل في فحص حالة البيانات الأولية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف جميع البيانات (للاختبار فقط)
export async function DELETE(request: NextRequest) {
  try {
    // هذا للاختبار فقط - في الإنتاج يجب إزالة هذه الدالة
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({
        error: 'غير مسموح في بيئة الإنتاج'
      }, { status: 403 })
    }

    // جلب جميع المنتجات وحذفها
    const allProducts = await ProductModel.getAll({ limit: 1000 })
    let deletedCount = 0

    for (const product of allProducts.products) {
      try {
        await ProductModel.delete(product.id)
        deletedCount++
      } catch (error) {
        console.error(`فشل في حذف المنتج: ${product.name}`, error)
      }
    }

    return NextResponse.json({
      message: 'تم حذف جميع البيانات',
      deletedCount: deletedCount,
      status: 'cleared'
    })
  } catch (error) {
    console.error('خطأ في حذف البيانات:', error)
    return NextResponse.json({
      error: 'فشل في حذف البيانات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
