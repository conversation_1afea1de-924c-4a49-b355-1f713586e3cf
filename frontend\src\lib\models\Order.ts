import { query, transaction } from '../database'
import { PoolClient } from 'pg'

export interface Order {
  id: string
  user_id: string
  school_id?: string
  order_number: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  total_amount: number
  shipping_amount: number
  tax_amount: number
  discount_amount: number
  payment_method?: string
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  shipping_address?: any
  billing_address?: any
  notes?: string
  created_at: Date
  updated_at: Date
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  quantity: number
  unit_price: number
  total_price: number
  type: 'purchase' | 'rental'
  size?: string
  color?: string
  customizations?: any
  created_at: Date
}

export interface OrderWithItems extends Order {
  items: (OrderItem & {
    product_name: string
    product_image: string
  })[]
  user_name: string
  user_email: string
}

export interface CreateOrderData {
  user_id: string
  school_id?: string
  items: {
    product_id: string
    quantity: number
    unit_price: number
    type: 'purchase' | 'rental'
    size?: string
    color?: string
    customizations?: any
  }[]
  shipping_address?: any
  billing_address?: any
  payment_method?: string
  notes?: string
}

export class OrderModel {
  // إنشاء طلب جديد
  static async create(orderData: CreateOrderData): Promise<OrderWithItems> {
    return await transaction(async (client: PoolClient) => {
      // حساب المجاميع
      const subtotal = orderData.items.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0)
      const shipping_amount = subtotal > 500 ? 0 : 50 // شحن مجاني للطلبات أكثر من 500 درهم
      const tax_amount = subtotal * 0.20 // ضريبة 20%
      const total_amount = subtotal + shipping_amount + tax_amount

      // إنشاء رقم الطلب
      const order_number = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`

      // إنشاء الطلب
      const orderResult = await client.query(`
        INSERT INTO orders (
          user_id, school_id, order_number, total_amount, shipping_amount, tax_amount,
          shipping_address, billing_address, payment_method, notes
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) RETURNING *
      `, [
        orderData.user_id,
        orderData.school_id,
        order_number,
        total_amount,
        shipping_amount,
        tax_amount,
        JSON.stringify(orderData.shipping_address),
        JSON.stringify(orderData.billing_address),
        orderData.payment_method,
        orderData.notes
      ])

      const order = orderResult.rows[0]

      // إضافة عناصر الطلب
      const items: OrderItem[] = []
      for (const item of orderData.items) {
        const itemResult = await client.query(`
          INSERT INTO order_items (
            order_id, product_id, quantity, unit_price, total_price, type, size, color, customizations
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9
          ) RETURNING *
        `, [
          order.id,
          item.product_id,
          item.quantity,
          item.unit_price,
          item.unit_price * item.quantity,
          item.type,
          item.size,
          item.color,
          JSON.stringify(item.customizations)
        ])

        items.push(itemResult.rows[0])

        // تحديث المخزون
        await client.query(
          'UPDATE products SET stock_quantity = stock_quantity - $1 WHERE id = $2',
          [item.quantity, item.product_id]
        )
      }

      // جلب تفاصيل الطلب مع المنتجات والمستخدم
      return await this.getByIdWithDetails(order.id, client)
    })
  }

  // جلب طلب بالمعرف مع التفاصيل
  static async getByIdWithDetails(id: string, client?: PoolClient): Promise<OrderWithItems | null> {
    const queryFn = client ? client.query.bind(client) : query

    const result = await queryFn(`
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      WHERE o.id = $1
    `, [id])

    if (result.rows.length === 0) return null

    const order = result.rows[0]

    // جلب عناصر الطلب
    const itemsResult = await queryFn(`
      SELECT 
        oi.*,
        p.name as product_name,
        p.images[1] as product_image
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
      ORDER BY oi.created_at
    `, [id])

    return {
      ...order,
      items: itemsResult.rows
    }
  }

  // جلب طلبات المستخدم
  static async getByUserId(userId: string, limit: number = 20, offset: number = 0): Promise<OrderWithItems[]> {
    const result = await query(`
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      WHERE o.user_id = $1
      ORDER BY o.created_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset])

    const orders: OrderWithItems[] = []
    
    for (const order of result.rows) {
      const itemsResult = await query(`
        SELECT 
          oi.*,
          p.name as product_name,
          p.images[1] as product_image
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = $1
        ORDER BY oi.created_at
      `, [order.id])

      orders.push({
        ...order,
        items: itemsResult.rows
      })
    }

    return orders
  }

  // جلب جميع الطلبات مع الفلترة
  static async getAll(filters: {
    status?: string
    user_id?: string
    school_id?: string
    payment_status?: string
    search?: string
    limit?: number
    offset?: number
    sortBy?: 'created_at' | 'total_amount' | 'status'
    sortOrder?: 'ASC' | 'DESC'
  } = {}): Promise<{ orders: OrderWithItems[], total: number }> {
    let whereConditions: string[] = []
    let params: any[] = []
    let paramIndex = 1

    // بناء شروط WHERE
    if (filters.status) {
      whereConditions.push(`o.status = $${paramIndex}`)
      params.push(filters.status)
      paramIndex++
    }

    if (filters.user_id) {
      whereConditions.push(`o.user_id = $${paramIndex}`)
      params.push(filters.user_id)
      paramIndex++
    }

    if (filters.school_id) {
      whereConditions.push(`o.school_id = $${paramIndex}`)
      params.push(filters.school_id)
      paramIndex++
    }

    if (filters.payment_status) {
      whereConditions.push(`o.payment_status = $${paramIndex}`)
      params.push(filters.payment_status)
      paramIndex++
    }

    if (filters.search) {
      whereConditions.push(`(o.order_number ILIKE $${paramIndex} OR u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`)
      params.push(`%${filters.search}%`)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // ترتيب النتائج
    const sortBy = filters.sortBy || 'created_at'
    const sortOrder = filters.sortOrder || 'DESC'
    const orderClause = `ORDER BY o.${sortBy} ${sortOrder}`

    // حد النتائج والإزاحة
    const limit = filters.limit || 50
    const offset = filters.offset || 0
    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)

    // استعلام العد الكلي
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${whereClause}
    `
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)

    // استعلام البيانات
    const dataQuery = `
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${whereClause} 
      ${orderClause} 
      ${limitClause}
    `
    
    const result = await query(dataQuery, params)
    
    // جلب عناصر كل طلب
    const orders: OrderWithItems[] = []
    for (const order of result.rows) {
      const itemsResult = await query(`
        SELECT 
          oi.*,
          p.name as product_name,
          p.images[1] as product_image
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = $1
        ORDER BY oi.created_at
      `, [order.id])

      orders.push({
        ...order,
        items: itemsResult.rows
      })
    }
    
    return { orders, total }
  }

  // تحديث حالة الطلب
  static async updateStatus(id: string, status: Order['status']): Promise<Order | null> {
    const result = await query(
      'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
      [status, id]
    )

    return result.rows[0] || null
  }

  // تحديث حالة الدفع
  static async updatePaymentStatus(id: string, payment_status: Order['payment_status']): Promise<Order | null> {
    const result = await query(
      'UPDATE orders SET payment_status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
      [payment_status, id]
    )

    return result.rows[0] || null
  }

  // إلغاء الطلب
  static async cancel(id: string, reason?: string): Promise<boolean> {
    return await transaction(async (client: PoolClient) => {
      // تحديث حالة الطلب
      const orderResult = await client.query(
        'UPDATE orders SET status = $1, notes = COALESCE(notes, \'\') || $2, updated_at = NOW() WHERE id = $3 RETURNING *',
        ['cancelled', reason ? `\nسبب الإلغاء: ${reason}` : '', id]
      )

      if (orderResult.rows.length === 0) return false

      // إرجاع المخزون
      const itemsResult = await client.query(
        'SELECT product_id, quantity FROM order_items WHERE order_id = $1',
        [id]
      )

      for (const item of itemsResult.rows) {
        await client.query(
          'UPDATE products SET stock_quantity = stock_quantity + $1 WHERE id = $2',
          [item.quantity, item.product_id]
        )
      }

      return true
    })
  }

  // إحصائيات الطلبات
  static async getStats(): Promise<{
    total: number
    pending: number
    confirmed: number
    delivered: number
    cancelled: number
    totalRevenue: number
    averageOrderValue: number
  }> {
    const result = await query(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE status = 'pending') as pending,
        COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed,
        COUNT(*) FILTER (WHERE status = 'delivered') as delivered,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
        COALESCE(SUM(total_amount) FILTER (WHERE status != 'cancelled'), 0) as total_revenue,
        COALESCE(AVG(total_amount) FILTER (WHERE status != 'cancelled'), 0) as average_order_value
      FROM orders
    `)

    const stats = result.rows[0]
    return {
      total: parseInt(stats.total),
      pending: parseInt(stats.pending),
      confirmed: parseInt(stats.confirmed),
      delivered: parseInt(stats.delivered),
      cancelled: parseInt(stats.cancelled),
      totalRevenue: parseFloat(stats.total_revenue),
      averageOrderValue: parseFloat(stats.average_order_value)
    }
  }
}
