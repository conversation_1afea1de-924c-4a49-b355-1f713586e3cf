{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,kHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,eAAe;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,cAAc;YAC3D,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,kHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,kHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,8OAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,8OAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,8OAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEAAI,WAAU;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,8OAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;AAeO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAGO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const { cartCount, wishlistCount } = useCart()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        // جلب جميع عناصر القائمة (الرئيسية والفرعية)\n        const response = await fetch('/api/menu-items')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const { user, profile } = useAuth()\n\n  // تحديد عناصر القائمة بناءً على الحالة\n  let allNavItems: NavItem[]\n\n  if (loading) {\n    // أثناء التحميل، استخدم القائمة الافتراضية\n    allNavItems = defaultNavItems\n  } else if (menuItems.length > 0) {\n    // إذا كانت هناك عناصر من قاعدة البيانات، استخدمها فقط\n    allNavItems = getNavItemsFromDB()\n  } else {\n    // إذا لم تكن هناك عناصر من قاعدة البيانات، استخدم القائمة الافتراضية\n    allNavItems = defaultNavItems\n  }\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const hasSubItems = item.subItems && item.subItems.length > 0\n\n              // إذا كان العنصر له قوائم فرعية\n              if (hasSubItems) {\n                return (\n                  <div key={item.href} className=\"relative group\">\n                    <button\n                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                        isActive(item.href)\n                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                      }`}\n                    >\n                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                        {item.icon}\n                      </span>\n                      <span className=\"text-sm font-medium\">\n                        {item.label}\n                      </span>\n                      <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                    </button>\n\n                    {/* القائمة الفرعية */}\n                    <div className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                      <div className=\"py-2\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  </div>\n                )\n              }\n\n              // العناصر العادية بدون قوائم فرعية\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartCount > 99 ? '99+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartCount > 9 ? '9+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.href} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.href}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;;;AA0DO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAIb,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;gBACnC,OAAO;oBACL,6CAA6C;oBAC7C,QAAQ,IAAI,CAAC;oBACb,aAAa,EAAE;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,mDAAmD;gBACnD,QAAQ,IAAI,CAAC,kDAAkD;gBAC/D,aAAa,EAAE;YACjB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,+DAA+D;QAC/D,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS;QAE5E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,uCAAuC;YACvC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,uCAAuC;IACvC,IAAI;IAEJ,IAAI,SAAS;QACX,2CAA2C;QAC3C,cAAc;IAChB,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;QAC/B,sDAAsD;QACtD,cAAc;IAChB,OAAO;QACL,qEAAqE;QACrE,cAAc;IAChB;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;gCAE5D,gCAAgC;gCAChC,IAAI,aAAa;oCACf,qBACE,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;kEAEF,8OAAC;wDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;kEAChH,KAAK,IAAI;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;kEAEb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DANrB,QAAQ,IAAI;;;;;oDAUvB;;;;;;;;;;;;uCAtCI,KAAK,IAAI;;;;;gCA2CvB;gCAEA,mCAAmC;gCACnC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,8OAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,8OAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOlC,8OAAC,2JAAA,CAAA,uBAAoB;;;;;8CAGrB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;sDACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,8OAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,IAAI;;;;;oDAWvB;;;;;;;2CA7CM,KAAK,IAAI;;;;;oCAiDvB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;0DACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 2583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/checkoutSettings.ts"], "sourcesContent": ["// إعدادات صفحة الدفع القابلة للتخصيص\nexport interface CheckoutField {\n  id: string\n  name: string\n  label: string\n  type: 'text' | 'email' | 'tel' | 'textarea' | 'select' | 'checkbox'\n  required: boolean\n  enabled: boolean\n  placeholder?: string\n  options?: string[] // للحقول من نوع select\n  validation?: {\n    minLength?: number\n    maxLength?: number\n    pattern?: string\n  }\n  order: number\n  section: 'shipping' | 'billing' | 'personal'\n}\n\nexport interface PaymentMethodConfig {\n  id: string\n  name: string\n  description: string\n  icon: string\n  enabled: boolean\n  order: number\n  config?: {\n    requiresCard?: boolean\n    requiresBankDetails?: boolean\n    additionalFields?: CheckoutField[]\n  }\n}\n\nexport interface DeliveryOption {\n  id: string\n  name: string\n  description: string\n  price: number\n  estimatedDays: string\n  enabled: boolean\n  order: number\n  icon: string\n  restrictions?: {\n    minOrderValue?: number\n    maxOrderValue?: number\n    availableRegions?: string[]\n  }\n}\n\nexport interface CheckoutSettings {\n  fields: CheckoutField[]\n  paymentMethods: PaymentMethodConfig[]\n  deliveryOptions: DeliveryOption[]\n  general: {\n    requireTermsAcceptance: boolean\n    allowGuestCheckout: boolean\n    showOrderSummary: boolean\n    enableSpecialInstructions: boolean\n    defaultCountry: string\n    currency: string\n    taxRate: number\n  }\n}\n\n// الإعدادات الافتراضية\nexport const defaultCheckoutSettings: CheckoutSettings = {\n  fields: [\n    {\n      id: 'fullName',\n      name: 'fullName',\n      label: 'الاسم الكامل',\n      type: 'text',\n      required: true,\n      enabled: true,\n      placeholder: 'أدخل اسمك الكامل',\n      order: 1,\n      section: 'personal',\n      validation: { minLength: 2, maxLength: 100 }\n    },\n    {\n      id: 'email',\n      name: 'email',\n      label: 'البريد الإلكتروني',\n      type: 'email',\n      required: true,\n      enabled: true,\n      placeholder: '<EMAIL>',\n      order: 2,\n      section: 'personal'\n    },\n    {\n      id: 'phone',\n      name: 'phone',\n      label: 'رقم الهاتف',\n      type: 'tel',\n      required: true,\n      enabled: true,\n      placeholder: '+971-XX-XXX-XXXX',\n      order: 3,\n      section: 'personal'\n    },\n    {\n      id: 'address',\n      name: 'address',\n      label: 'العنوان',\n      type: 'textarea',\n      required: true,\n      enabled: true,\n      placeholder: 'أدخل عنوانك الكامل',\n      order: 4,\n      section: 'shipping'\n    },\n    {\n      id: 'city',\n      name: 'city',\n      label: 'المدينة',\n      type: 'text',\n      required: true,\n      enabled: true,\n      placeholder: 'اسم المدينة',\n      order: 5,\n      section: 'shipping'\n    },\n    {\n      id: 'state',\n      name: 'state',\n      label: 'الإمارة/المنطقة',\n      type: 'select',\n      required: true,\n      enabled: true,\n      order: 6,\n      section: 'shipping',\n      options: ['أبوظبي', 'دبي', 'الشارقة', 'عجمان', 'أم القيوين', 'رأس الخيمة', 'الفجيرة']\n    },\n    {\n      id: 'zipCode',\n      name: 'zipCode',\n      label: 'الرمز البريدي',\n      type: 'text',\n      required: false,\n      enabled: true,\n      placeholder: '12345',\n      order: 7,\n      section: 'shipping'\n    },\n    {\n      id: 'specialInstructions',\n      name: 'specialInstructions',\n      label: 'تعليمات خاصة',\n      type: 'textarea',\n      required: false,\n      enabled: true,\n      placeholder: 'أي تعليمات خاصة للتوصيل...',\n      order: 8,\n      section: 'shipping'\n    }\n  ],\n  \n  paymentMethods: [\n    {\n      id: 'card',\n      name: 'بطاقة ائتمان/خصم',\n      description: 'Visa, Mastercard, American Express',\n      icon: 'CreditCard',\n      enabled: true,\n      order: 1,\n      config: {\n        requiresCard: true,\n        additionalFields: [\n          {\n            id: 'cardNumber',\n            name: 'cardNumber',\n            label: 'رقم البطاقة',\n            type: 'text',\n            required: true,\n            enabled: true,\n            placeholder: '1234 5678 9012 3456',\n            order: 1,\n            section: 'billing'\n          },\n          {\n            id: 'expiryDate',\n            name: 'expiryDate',\n            label: 'تاريخ الانتهاء',\n            type: 'text',\n            required: true,\n            enabled: true,\n            placeholder: 'MM/YY',\n            order: 2,\n            section: 'billing'\n          },\n          {\n            id: 'cvv',\n            name: 'cvv',\n            label: 'رمز الأمان',\n            type: 'text',\n            required: true,\n            enabled: true,\n            placeholder: '123',\n            order: 3,\n            section: 'billing'\n          }\n        ]\n      }\n    },\n    {\n      id: 'cash',\n      name: 'الدفع عند الاستلام',\n      description: 'ادفع نقداً عند وصول الطلب',\n      icon: 'Banknote',\n      enabled: true,\n      order: 2\n    },\n    {\n      id: 'bank_transfer',\n      name: 'تحويل بنكي',\n      description: 'تحويل مباشر إلى حساب البنك',\n      icon: 'Building2',\n      enabled: true,\n      order: 3\n    },\n    {\n      id: 'digital_wallet',\n      name: 'المحفظة الرقمية',\n      description: 'Apple Pay, Google Pay, Samsung Pay',\n      icon: 'Smartphone',\n      enabled: false,\n      order: 4\n    }\n  ],\n  \n  deliveryOptions: [\n    {\n      id: 'standard',\n      name: 'التوصيل العادي',\n      description: '3-5 أيام عمل',\n      price: 25,\n      estimatedDays: '3-5 أيام',\n      enabled: true,\n      order: 1,\n      icon: 'Truck'\n    },\n    {\n      id: 'express',\n      name: 'التوصيل السريع',\n      description: '1-2 أيام عمل',\n      price: 50,\n      estimatedDays: '1-2 أيام',\n      enabled: true,\n      order: 2,\n      icon: 'Zap',\n      restrictions: {\n        minOrderValue: 100\n      }\n    },\n    {\n      id: 'same_day',\n      name: 'التوصيل في نفس اليوم',\n      description: 'خلال 6 ساعات',\n      price: 100,\n      estimatedDays: '6 ساعات',\n      enabled: true,\n      order: 3,\n      icon: 'Clock',\n      restrictions: {\n        minOrderValue: 200,\n        availableRegions: ['دبي', 'أبوظبي']\n      }\n    },\n    {\n      id: 'pickup',\n      name: 'الاستلام من المتجر',\n      description: 'استلم طلبك من فرعنا',\n      price: 0,\n      estimatedDays: 'فوري',\n      enabled: false,\n      order: 4,\n      icon: 'Store'\n    }\n  ],\n  \n  general: {\n    requireTermsAcceptance: true,\n    allowGuestCheckout: true,\n    showOrderSummary: true,\n    enableSpecialInstructions: true,\n    defaultCountry: 'الإمارات العربية المتحدة',\n    currency: 'AED',\n    taxRate: 0.05\n  }\n}\n\n// مدير إعدادات الدفع\nexport class CheckoutSettingsManager {\n  private static getStorageKey(): string {\n    return 'checkoutSettings'\n  }\n\n  static getSettings(): CheckoutSettings {\n    if (typeof window === 'undefined') return defaultCheckoutSettings\n\n    const stored = localStorage.getItem(this.getStorageKey())\n    return stored ? JSON.parse(stored) : defaultCheckoutSettings\n  }\n\n  static saveSettings(settings: CheckoutSettings): void {\n    if (typeof window === 'undefined') return\n    localStorage.setItem(this.getStorageKey(), JSON.stringify(settings))\n  }\n\n  static resetToDefaults(): void {\n    this.saveSettings(defaultCheckoutSettings)\n  }\n\n  // إدارة الحقول\n  static addField(field: CheckoutField): void {\n    const settings = this.getSettings()\n    settings.fields.push(field)\n    this.saveSettings(settings)\n  }\n\n  static updateField(fieldId: string, updates: Partial<CheckoutField>): void {\n    const settings = this.getSettings()\n    const fieldIndex = settings.fields.findIndex(f => f.id === fieldId)\n    if (fieldIndex !== -1) {\n      settings.fields[fieldIndex] = { ...settings.fields[fieldIndex], ...updates }\n      this.saveSettings(settings)\n    }\n  }\n\n  static removeField(fieldId: string): void {\n    const settings = this.getSettings()\n    settings.fields = settings.fields.filter(f => f.id !== fieldId)\n    this.saveSettings(settings)\n  }\n\n  // إدارة طرق الدفع\n  static updatePaymentMethod(methodId: string, updates: Partial<PaymentMethodConfig>): void {\n    const settings = this.getSettings()\n    const methodIndex = settings.paymentMethods.findIndex(m => m.id === methodId)\n    if (methodIndex !== -1) {\n      settings.paymentMethods[methodIndex] = { ...settings.paymentMethods[methodIndex], ...updates }\n      this.saveSettings(settings)\n    }\n  }\n\n  // إدارة خيارات التوصيل\n  static updateDeliveryOption(optionId: string, updates: Partial<DeliveryOption>): void {\n    const settings = this.getSettings()\n    const optionIndex = settings.deliveryOptions.findIndex(o => o.id === optionId)\n    if (optionIndex !== -1) {\n      settings.deliveryOptions[optionIndex] = { ...settings.deliveryOptions[optionIndex], ...updates }\n      this.saveSettings(settings)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;AAiE9B,MAAM,0BAA4C;IACvD,QAAQ;QACN;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;YACT,YAAY;gBAAE,WAAW;gBAAG,WAAW;YAAI;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YAC<PERSON>,MAAM;YAC<PERSON>,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;gBAAC;gBAAU;gBAAO;gBAAW;gBAAS;gBAAc;gBAAc;aAAU;QACvF;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,gBAAgB;QACd;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;gBACN,cAAc;gBACd,kBAAkB;oBAChB;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,OAAO;wBACP,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,MAAM;wBACN,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,OAAO;wBACP,SAAS;oBACX;iBACD;YACH;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;QACT;KACD;IAED,iBAAiB;QACf;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,eAAe;YACf,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,eAAe;YACf,SAAS;YACT,OAAO;YACP,MAAM;YACN,cAAc;gBACZ,eAAe;YACjB;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,eAAe;YACf,SAAS;YACT,OAAO;YACP,MAAM;YACN,cAAc;gBACZ,eAAe;gBACf,kBAAkB;oBAAC;oBAAO;iBAAS;YACrC;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;YACP,eAAe;YACf,SAAS;YACT,OAAO;YACP,MAAM;QACR;KACD;IAED,SAAS;QACP,wBAAwB;QACxB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,gBAAgB;QAChB,UAAU;QACV,SAAS;IACX;AACF;AAGO,MAAM;IACX,OAAe,gBAAwB;QACrC,OAAO;IACT;IAEA,OAAO,cAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,aAAa,QAA0B,EAAQ;QACpD,wCAAmC;;IAErC;IAEA,OAAO,kBAAwB;QAC7B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,eAAe;IACf,OAAO,SAAS,KAAoB,EAAQ;QAC1C,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,MAAM,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,YAAY,OAAe,EAAE,OAA+B,EAAQ;QACzE,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,aAAa,SAAS,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC3D,IAAI,eAAe,CAAC,GAAG;YACrB,SAAS,MAAM,CAAC,WAAW,GAAG;gBAAE,GAAG,SAAS,MAAM,CAAC,WAAW;gBAAE,GAAG,OAAO;YAAC;YAC3E,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEA,OAAO,YAAY,OAAe,EAAQ;QACxC,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,MAAM,GAAG,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,kBAAkB;IAClB,OAAO,oBAAoB,QAAgB,EAAE,OAAqC,EAAQ;QACxF,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,cAAc,SAAS,cAAc,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpE,IAAI,gBAAgB,CAAC,GAAG;YACtB,SAAS,cAAc,CAAC,YAAY,GAAG;gBAAE,GAAG,SAAS,cAAc,CAAC,YAAY;gBAAE,GAAG,OAAO;YAAC;YAC7F,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEA,uBAAuB;IACvB,OAAO,qBAAqB,QAAgB,EAAE,OAAgC,EAAQ;QACpF,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,cAAc,SAAS,eAAe,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrE,IAAI,gBAAgB,CAAC,GAAG;YACtB,SAAS,eAAe,CAAC,YAAY,GAAG;gBAAE,GAAG,SAAS,eAAe,CAAC,YAAY;gBAAE,GAAG,OAAO;YAAC;YAC/F,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;AACF", "debugId": null}}, {"offset": {"line": 2894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 3172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/checkout/DynamicFormField.tsx"], "sourcesContent": ["\"use client\"\n\nimport { CheckoutField } from '@/lib/checkoutSettings'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\n\ninterface DynamicFormFieldProps {\n  field: CheckoutField\n  value: any\n  onChange: (value: any) => void\n  error?: string\n}\n\nexport function DynamicFormField({ field, value, onChange, error }: DynamicFormFieldProps) {\n  if (!field.enabled) return null\n\n  const renderField = () => {\n    switch (field.type) {\n      case 'text':\n      case 'email':\n      case 'tel':\n        return (\n          <Input\n            type={field.type}\n            id={field.id}\n            value={value || ''}\n            onChange={(e) => onChange(e.target.value)}\n            placeholder={field.placeholder}\n            required={field.required}\n            className={error ? 'border-red-500' : ''}\n          />\n        )\n\n      case 'textarea':\n        return (\n          <Textarea\n            id={field.id}\n            value={value || ''}\n            onChange={(e) => onChange(e.target.value)}\n            placeholder={field.placeholder}\n            required={field.required}\n            className={error ? 'border-red-500' : ''}\n            rows={3}\n          />\n        )\n\n      case 'select':\n        return (\n          <Select value={value || ''} onValueChange={onChange}>\n            <SelectTrigger className={error ? 'border-red-500' : ''}>\n              <SelectValue placeholder={field.placeholder || `اختر ${field.label}`} />\n            </SelectTrigger>\n            <SelectContent>\n              {field.options?.map((option) => (\n                <SelectItem key={option} value={option}>\n                  {option}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        )\n\n      case 'checkbox':\n        return (\n          <div className=\"flex items-center space-x-2 space-x-reverse\">\n            <Checkbox\n              id={field.id}\n              checked={value || false}\n              onCheckedChange={onChange}\n            />\n            <Label htmlFor={field.id} className=\"text-sm arabic-text\">\n              {field.placeholder || field.label}\n            </Label>\n          </div>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className=\"space-y-2\">\n      {field.type !== 'checkbox' && (\n        <Label htmlFor={field.id} className=\"arabic-text\">\n          {field.label}\n          {field.required && <span className=\"text-red-500 mr-1\">*</span>}\n        </Label>\n      )}\n      {renderField()}\n      {error && (\n        <p className=\"text-sm text-red-500 arabic-text\">{error}</p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AAgBO,SAAS,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAyB;IACvF,IAAI,CAAC,MAAM,OAAO,EAAE,OAAO;IAE3B,MAAM,cAAc;QAClB,OAAQ,MAAM,IAAI;YAChB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBACJ,MAAM,MAAM,IAAI;oBAChB,IAAI,MAAM,EAAE;oBACZ,OAAO,SAAS;oBAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACxC,aAAa,MAAM,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,WAAW,QAAQ,mBAAmB;;;;;;YAI5C,KAAK;gBACH,qBACE,8OAAC,oIAAA,CAAA,WAAQ;oBACP,IAAI,MAAM,EAAE;oBACZ,OAAO,SAAS;oBAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oBACxC,aAAa,MAAM,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,WAAW,QAAQ,mBAAmB;oBACtC,MAAM;;;;;;YAIZ,KAAK;gBACH,qBACE,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO,SAAS;oBAAI,eAAe;;sCACzC,8OAAC,kIAAA,CAAA,gBAAa;4BAAC,WAAW,QAAQ,mBAAmB;sCACnD,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,aAAa,MAAM,WAAW,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;sCAEtE,8OAAC,kIAAA,CAAA,gBAAa;sCACX,MAAM,OAAO,EAAE,IAAI,CAAC,uBACnB,8OAAC,kIAAA,CAAA,aAAU;oCAAc,OAAO;8CAC7B;mCADc;;;;;;;;;;;;;;;;YAQ3B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAI,MAAM,EAAE;4BACZ,SAAS,SAAS;4BAClB,iBAAiB;;;;;;sCAEnB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS,MAAM,EAAE;4BAAE,WAAU;sCACjC,MAAM,WAAW,IAAI,MAAM,KAAK;;;;;;;;;;;;YAKzC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,MAAM,IAAI,KAAK,4BACd,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,MAAM,EAAE;gBAAE,WAAU;;oBACjC,MAAM,KAAK;oBACX,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;YAG1D;YACA,uBACC,8OAAC;gBAAE,WAAU;0BAAoC;;;;;;;;;;;;AAIzD", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction RadioGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Root>) {\n  return (\n    <RadioGroupPrimitive.Root\n      data-slot=\"radio-group\"\n      className={cn(\"grid gap-3\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction RadioGroupItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Item>) {\n  return (\n    <RadioGroupPrimitive.Item\n      data-slot=\"radio-group-item\"\n      className={cn(\n        \"border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator\n        data-slot=\"radio-group-indicator\"\n        className=\"relative flex items-center justify-center\"\n      >\n        <CircleIcon className=\"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n}\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0XACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAC5B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 3439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/checkout/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { Navigation } from '@/components/Navigation'\nimport { CheckoutSettingsManager, CheckoutSettings } from '@/lib/checkoutSettings'\nimport { DynamicFormField } from '@/components/checkout/DynamicFormField'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Separator } from '@/components/ui/separator'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { \n  CreditCard,\n  MapPin,\n  User,\n  Phone,\n  Mail,\n  Calendar,\n  Shield,\n  Truck,\n  CheckCircle,\n  AlertCircle,\n  ArrowLeft,\n  ArrowRight\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface ShippingAddress {\n  fullName: string\n  phone: string\n  email: string\n  address: string\n  city: string\n  state: string\n  zipCode: string\n  country: string\n}\n\ninterface PaymentMethod {\n  type: 'card' | 'cash' | 'bank_transfer'\n  cardNumber?: string\n  expiryDate?: string\n  cvv?: string\n  cardholderName?: string\n}\n\nexport default function CheckoutPage() {\n  const { user, profile } = useAuth()\n  const { cartItems, getCartTotal } = useCart()\n  const [checkoutSettings, setCheckoutSettings] = useState<CheckoutSettings | null>(null)\n  const [currentStep, setCurrentStep] = useState(1)\n  const [formData, setFormData] = useState<Record<string, any>>({})\n  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState('')\n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')\n  const [specialInstructions, setSpecialInstructions] = useState('')\n  const [agreeToTerms, setAgreeToTerms] = useState(false)\n  const [loading, setLoading] = useState(false)\n\n  // بيانات وهمية للطلب\n  const orderSummary = {\n    items: [\n      { name: 'فستان تخرج كلاسيكي', quantity: 1, price: 450 },\n      { name: 'قبعة التخرج', quantity: 1, price: 75 }\n    ],\n    subtotal: 525,\n    tax: 26.25,\n    shipping: 25,\n    total: 576.25\n  }\n\n  // تحميل إعدادات الدفع\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const settings = await CheckoutSettingsManager.getSettings()\n        setCheckoutSettings(settings)\n        \n        // تعيين القيم الافتراضية\n        if (settings.deliveryOptions.length > 0) {\n          const defaultDelivery = settings.deliveryOptions.find(opt => opt.enabled)\n          if (defaultDelivery) {\n            setSelectedDeliveryOption(defaultDelivery.id)\n          }\n        }\n        \n        if (settings.paymentMethods.length > 0) {\n          const defaultPayment = settings.paymentMethods.find(method => method.enabled)\n          if (defaultPayment) {\n            setSelectedPaymentMethod(defaultPayment.id)\n          }\n        }\n      } catch (error) {\n        console.error('Error loading checkout settings:', error)\n      }\n    }\n\n    loadSettings()\n  }, [])\n\n  const updateFormData = (fieldId: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldId]: value\n    }))\n  }\n\n  const validateForm = () => {\n    if (!checkoutSettings) return false\n\n    // التحقق من الحقول المطلوبة\n    const requiredFields = checkoutSettings.fields.filter(field => field.required && field.enabled)\n    \n    for (const field of requiredFields) {\n      if (!formData[field.id] || formData[field.id].toString().trim() === '') {\n        alert(`الرجاء إدخال ${field.label}`)\n        return false\n      }\n    }\n\n    // التحقق من طريقة التوصيل\n    if (!selectedDeliveryOption) {\n      alert('الرجاء اختيار طريقة التوصيل')\n      return false\n    }\n\n    // التحقق من طريقة الدفع\n    if (!selectedPaymentMethod) {\n      alert('الرجاء اختيار طريقة الدفع')\n      return false\n    }\n\n    // التحقق من الموافقة على الشروط\n    if (checkoutSettings.general.requireTermsAcceptance && !agreeToTerms) {\n      alert('يرجى الموافقة على الشروط والأحكام')\n      return false\n    }\n\n    return true\n  }\n\n  const handleSubmitOrder = async () => {\n    if (!validateForm()) return\n\n    setLoading(true)\n\n    // محاكاة معالجة الطلب\n    setTimeout(() => {\n      setLoading(false)\n\n      // توجيه حسب طريقة الدفع المختارة\n      if (selectedPaymentMethod === 'bank_transfer') {\n        // توجيه إلى صفحة الدفع البنكي\n        const orderId = 'ORD-' + Date.now()\n        const totalAmount = orderSummary.total\n        window.location.href = `/payment/bank-transfer?order_id=${orderId}&amount=${totalAmount}`\n      } else {\n        // توجيه إلى صفحة تأكيد الطلب للطرق الأخرى\n        window.location.href = '/order-confirmation'\n      }\n    }, 2000)\n  }\n\n  const steps = [\n    { id: 1, name: 'معلومات الشحن', icon: 'MapPin' },\n    { id: 2, name: 'طريقة التوصيل', icon: 'Truck' },\n    { id: 3, name: 'طريقة الدفع', icon: 'CreditCard' },\n    { id: 4, name: 'مراجعة الطلب', icon: 'CheckCircle' }\n  ]\n\n  const getStepIcon = (iconName: string) => {\n    switch (iconName) {\n      case 'MapPin': return <MapPin className=\"h-4 w-4\" />\n      case 'Truck': return <Truck className=\"h-4 w-4\" />\n      case 'CreditCard': return <CreditCard className=\"h-4 w-4\" />\n      case 'CheckCircle': return <CheckCircle className=\"h-4 w-4\" />\n      default: return <CheckCircle className=\"h-4 w-4\" />\n    }\n  }\n\n  if (!checkoutSettings) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n        <Navigation />\n        <main className=\"container mx-auto px-4 py-8\">\n          <div className=\"text-center py-16\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600 dark:text-gray-400\">جاري تحميل صفحة الدفع...</p>\n          </div>\n        </main>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <Button variant=\"outline\" size=\"sm\" asChild className=\"mb-4\">\n            <a href=\"/cart\">\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              العودة للسلة\n            </a>\n          </Button>\n          \n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text\">\n            إتمام الطلب 💳\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n            أكمل معلوماتك لإتمام عملية الشراء\n          </p>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                  currentStep >= step.id\n                    ? 'bg-blue-600 border-blue-600 text-white'\n                    : 'border-gray-300 text-gray-400'\n                }`}>\n                  {currentStep > step.id ? (\n                    <CheckCircle className=\"h-5 w-5\" />\n                  ) : (\n                    getStepIcon(step.icon)\n                  )}\n                </div>\n                <span className={`ml-3 text-sm font-medium arabic-text ${\n                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'\n                }`}>\n                  {step.name}\n                </span>\n                {index < steps.length - 1 && (\n                  <div className={`w-16 h-0.5 mx-4 ${\n                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2\">\n            {/* Step 1: Customer Information */}\n            {currentStep === 1 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <User className=\"h-5 w-5\" />\n                    معلومات العميل\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    أدخل معلوماتك الشخصية ومعلومات التوصيل\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  {/* Personal Information */}\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4\">\n                      المعلومات الشخصية\n                    </h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      {checkoutSettings.fields\n                        .filter(field => field.section === 'personal' && field.enabled)\n                        .sort((a, b) => a.order - b.order)\n                        .map(field => (\n                          <DynamicFormField\n                            key={field.id}\n                            field={field}\n                            value={formData[field.id]}\n                            onChange={(value) => updateFormData(field.id, value)}\n                          />\n                        ))}\n                    </div>\n                  </div>\n\n                  <Separator />\n\n                  {/* Shipping Information */}\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4\">\n                      معلومات التوصيل\n                    </h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      {checkoutSettings.fields\n                        .filter(field => field.section === 'shipping' && field.enabled)\n                        .sort((a, b) => a.order - b.order)\n                        .map(field => (\n                          <DynamicFormField\n                            key={field.id}\n                            field={field}\n                            value={formData[field.id]}\n                            onChange={(value) => updateFormData(field.id, value)}\n                          />\n                        ))}\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-end\">\n                    <Button onClick={() => setCurrentStep(2)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 2: Delivery Options */}\n            {currentStep === 2 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <Truck className=\"h-5 w-5\" />\n                    طريقة التوصيل\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    اختر طريقة التوصيل المناسبة لك\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <RadioGroup value={selectedDeliveryOption} onValueChange={setSelectedDeliveryOption}>\n                    <div className=\"space-y-4\">\n                      {checkoutSettings.deliveryOptions\n                        .filter(option => option.enabled)\n                        .sort((a, b) => a.order - b.order)\n                        .map((option) => (\n                          <div key={option.id} className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value={option.id} id={option.id} />\n                            <Label htmlFor={option.id} className=\"flex-1 cursor-pointer\">\n                              <div className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800\">\n                                <div className=\"flex items-center gap-3\">\n                                  <Truck className=\"h-5 w-5 text-blue-600\" />\n                                  <div>\n                                    <p className=\"font-medium arabic-text\">{option.name}</p>\n                                    <p className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">\n                                      {option.description} - {option.estimatedDays}\n                                    </p>\n                                    {option.restrictions?.minOrderValue && (\n                                      <p className=\"text-xs text-orange-600 arabic-text\">\n                                        الحد الأدنى للطلب: {option.restrictions.minOrderValue} درهم\n                                      </p>\n                                    )}\n                                  </div>\n                                </div>\n                                <span className=\"font-bold text-green-600\">\n                                  {option.price === 0 ? 'مجاني' : `${option.price} درهم`}\n                                </span>\n                              </div>\n                            </Label>\n                          </div>\n                        ))}\n                    </div>\n                  </RadioGroup>\n\n                  {checkoutSettings.general.enableSpecialInstructions && (\n                    <div className=\"mt-6\">\n                      <Label htmlFor=\"instructions\" className=\"arabic-text\">تعليمات خاصة (اختياري)</Label>\n                      <Textarea\n                        id=\"instructions\"\n                        value={specialInstructions}\n                        onChange={(e) => setSpecialInstructions(e.target.value)}\n                        placeholder=\"أي تعليمات خاصة للتوصيل...\"\n                        className=\"arabic-text\"\n                      />\n                    </div>\n                  )}\n\n                  <div className=\"flex justify-between mt-6\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(1)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button onClick={() => setCurrentStep(3)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 3: Payment Method */}\n            {currentStep === 3 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <CreditCard className=\"h-5 w-5\" />\n                    طريقة الدفع\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    اختر طريقة الدفع المفضلة لديك\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>\n                    <div className=\"space-y-4\">\n                      {checkoutSettings.paymentMethods\n                        .filter(method => method.enabled)\n                        .sort((a, b) => a.order - b.order)\n                        .map((method) => (\n                          <div key={method.id} className=\"flex items-center space-x-2\">\n                            <RadioGroupItem value={method.id} id={method.id} />\n                            <Label htmlFor={method.id} className=\"flex-1 cursor-pointer\">\n                              <div className=\"flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800\">\n                                <CreditCard className=\"h-5 w-5 text-blue-600\" />\n                                <div>\n                                  <p className=\"font-medium arabic-text\">{method.name}</p>\n                                  <p className=\"text-sm text-gray-600 dark:text-gray-400 arabic-text\">\n                                    {method.description}\n                                  </p>\n                                </div>\n                              </div>\n                            </Label>\n                          </div>\n                        ))}\n                    </div>\n                  </RadioGroup>\n\n                  {/* Payment Method Specific Fields */}\n                  {(() => {\n                    const selectedMethod = checkoutSettings.paymentMethods.find(m => m.id === selectedPaymentMethod)\n                    if (selectedMethod?.config?.additionalFields) {\n                      return (\n                        <div className=\"mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800\">\n                          <h3 className=\"font-medium arabic-text\">معلومات الدفع</h3>\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                            {selectedMethod.config.additionalFields.map(field => (\n                              <DynamicFormField\n                                key={field.id}\n                                field={field}\n                                value={formData[field.id]}\n                                onChange={(value) => updateFormData(field.id, value)}\n                              />\n                            ))}\n                          </div>\n                        </div>\n                      )\n                    }\n                    return null\n                  })()}\n\n                  <div className=\"flex justify-between mt-6\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(2)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button onClick={() => setCurrentStep(4)} className=\"arabic-text\">\n                      التالي\n                      <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Step 4: Order Review */}\n            {currentStep === 4 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2 arabic-text\">\n                    <CheckCircle className=\"h-5 w-5\" />\n                    مراجعة الطلب\n                  </CardTitle>\n                  <CardDescription className=\"arabic-text\">\n                    راجع تفاصيل طلبك قبل التأكيد\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  {/* Order Items */}\n                  <div>\n                    <h3 className=\"font-medium mb-3 arabic-text\">المنتجات</h3>\n                    <div className=\"space-y-2\">\n                      {orderSummary.items.map((item, index) => (\n                        <div key={index} className=\"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                          <span className=\"arabic-text\">{item.name} × {item.quantity}</span>\n                          <span>{item.price} درهم</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Terms and Conditions */}\n                  {checkoutSettings.general.requireTermsAcceptance && (\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <Checkbox\n                        id=\"terms\"\n                        checked={agreeToTerms}\n                        onCheckedChange={setAgreeToTerms}\n                      />\n                      <Label htmlFor=\"terms\" className=\"text-sm arabic-text\">\n                        أوافق على <a href=\"/terms\" className=\"text-blue-600 hover:underline\">الشروط والأحكام</a> و\n                        <a href=\"/privacy\" className=\"text-blue-600 hover:underline\">سياسة الخصوصية</a>\n                      </Label>\n                    </div>\n                  )}\n\n                  <div className=\"flex justify-between\">\n                    <Button variant=\"outline\" onClick={() => setCurrentStep(3)} className=\"arabic-text\">\n                      <ArrowRight className=\"h-4 w-4 ml-2\" />\n                      السابق\n                    </Button>\n                    <Button \n                      onClick={handleSubmitOrder} \n                      disabled={(checkoutSettings.general.requireTermsAcceptance && !agreeToTerms) || loading}\n                      className=\"arabic-text\"\n                    >\n                      {loading ? 'جاري المعالجة...' : 'تأكيد الطلب'}\n                      <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n\n          {/* Order Summary Sidebar */}\n          {checkoutSettings.general.showOrderSummary && (\n            <div className=\"lg:col-span-1\">\n              <Card className=\"sticky top-24\">\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">ملخص الطلب</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    {orderSummary.items.map((item, index) => (\n                      <div key={index} className=\"flex justify-between text-sm\">\n                        <span className=\"arabic-text\">{item.name} × {item.quantity}</span>\n                        <span>{item.price} درهم</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <Separator />\n\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"arabic-text\">المجموع الفرعي:</span>\n                      <span>{orderSummary.subtotal} درهم</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"arabic-text\">الضريبة:</span>\n                      <span>{orderSummary.tax} درهم</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"arabic-text\">الشحن:</span>\n                      <span>{orderSummary.shipping} درهم</span>\n                    </div>\n                  </div>\n\n                  <Separator />\n\n                  <div className=\"flex justify-between font-bold text-lg\">\n                    <span className=\"arabic-text\">الإجمالي:</span>\n                    <span>{orderSummary.total} درهم</span>\n                  </div>\n\n                  <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\">\n                    <Shield className=\"h-4 w-4\" />\n                    <span className=\"arabic-text\">دفع آمن ومضمون</span>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjBA;;;;;;;;;;;;;;;;AAoDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qBAAqB;IACrB,MAAM,eAAe;QACnB,OAAO;YACL;gBAAE,MAAM;gBAAsB,UAAU;gBAAG,OAAO;YAAI;YACtD;gBAAE,MAAM;gBAAe,UAAU;gBAAG,OAAO;YAAG;SAC/C;QACD,UAAU;QACV,KAAK;QACL,UAAU;QACV,OAAO;IACT;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,WAAW,MAAM,8HAAA,CAAA,0BAAuB,CAAC,WAAW;gBAC1D,oBAAoB;gBAEpB,yBAAyB;gBACzB,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,GAAG;oBACvC,MAAM,kBAAkB,SAAS,eAAe,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO;oBACxE,IAAI,iBAAiB;wBACnB,0BAA0B,gBAAgB,EAAE;oBAC9C;gBACF;gBAEA,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;oBACtC,MAAM,iBAAiB,SAAS,cAAc,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,OAAO;oBAC5E,IAAI,gBAAgB;wBAClB,yBAAyB,eAAe,EAAE;oBAC5C;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC,SAAiB;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,kBAAkB,OAAO;QAE9B,4BAA4B;QAC5B,MAAM,iBAAiB,iBAAiB,MAAM,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,IAAI,MAAM,OAAO;QAE9F,KAAK,MAAM,SAAS,eAAgB;YAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,OAAO,IAAI;gBACtE,MAAM,CAAC,aAAa,EAAE,MAAM,KAAK,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,0BAA0B;QAC1B,IAAI,CAAC,wBAAwB;YAC3B,MAAM;YACN,OAAO;QACT;QAEA,wBAAwB;QACxB,IAAI,CAAC,uBAAuB;YAC1B,MAAM;YACN,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,iBAAiB,OAAO,CAAC,sBAAsB,IAAI,CAAC,cAAc;YACpE,MAAM;YACN,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QAEX,sBAAsB;QACtB,WAAW;YACT,WAAW;YAEX,iCAAiC;YACjC,IAAI,0BAA0B,iBAAiB;gBAC7C,8BAA8B;gBAC9B,MAAM,UAAU,SAAS,KAAK,GAAG;gBACjC,MAAM,cAAc,aAAa,KAAK;gBACtC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,gCAAgC,EAAE,QAAQ,QAAQ,EAAE,aAAa;YAC3F,OAAO;gBACL,0CAA0C;gBAC1C,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,GAAG;IACL;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,MAAM;YAAiB,MAAM;QAAS;QAC/C;YAAE,IAAI;YAAG,MAAM;YAAiB,MAAM;QAAQ;QAC9C;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;QAAa;QACjD;YAAE,IAAI;YAAG,MAAM;YAAgB,MAAM;QAAc;KACpD;IAED,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAU,qBAAO,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACtC,KAAK;gBAAc,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAe,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,IAAI,CAAC,kBAAkB;QACrB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;8BACX,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;;IAK/D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAEX,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,OAAO;gCAAC,WAAU;0CACpD,cAAA,8OAAC;oCAAE,MAAK;;sDACN,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAK1C,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC;4CAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,KAAK,EAAE,GAClB,2CACA,iCACJ;sDACC,cAAc,KAAK,EAAE,iBACpB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;uDAEvB,YAAY,KAAK,IAAI;;;;;;sDAGzB,8OAAC;4CAAK,WAAW,CAAC,qCAAqC,EACrD,eAAe,KAAK,EAAE,GAAG,kBAAkB,iBAC3C;sDACC,KAAK,IAAI;;;;;;wCAEX,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAW,CAAC,gBAAgB,EAC/B,cAAc,KAAK,EAAE,GAAG,gBAAgB,eACxC;;;;;;;mCApBI,KAAK,EAAE;;;;;;;;;;;;;;;kCA2BvB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCAEZ,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG9B,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqE;;;;;;0EAGnF,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,MAAM,CACrB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,cAAc,MAAM,OAAO,EAC7D,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAA,sBACH,8OAAC,kJAAA,CAAA,mBAAgB;wEAEf,OAAO;wEACP,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC;wEACzB,UAAU,CAAC,QAAU,eAAe,MAAM,EAAE,EAAE;uEAHzC,MAAM,EAAE;;;;;;;;;;;;;;;;kEASvB,8OAAC,qIAAA,CAAA,YAAS;;;;;kEAGV,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqE;;;;;;0EAGnF,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,MAAM,CACrB,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,cAAc,MAAM,OAAO,EAC7D,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAA,sBACH,8OAAC,kJAAA,CAAA,mBAAgB;wEAEf,OAAO;wEACP,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC;wEACzB,UAAU,CAAC,QAAU,eAAe,MAAM,EAAE,EAAE;uEAHzC,MAAM,EAAE;;;;;;;;;;;;;;;;kEASvB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS,IAAM,eAAe;4DAAI,WAAU;;gEAAc;8EAEhE,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC,0IAAA,CAAA,aAAU;wDAAC,OAAO;wDAAwB,eAAe;kEACxD,cAAA,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,eAAe,CAC9B,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,EAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAC,uBACJ,8OAAC;oEAAoB,WAAU;;sFAC7B,8OAAC,0IAAA,CAAA,iBAAc;4EAAC,OAAO,OAAO,EAAE;4EAAE,IAAI,OAAO,EAAE;;;;;;sFAC/C,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAS,OAAO,EAAE;4EAAE,WAAU;sFACnC,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC,oMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;0GACjB,8OAAC;;kHACC,8OAAC;wGAAE,WAAU;kHAA2B,OAAO,IAAI;;;;;;kHACnD,8OAAC;wGAAE,WAAU;;4GACV,OAAO,WAAW;4GAAC;4GAAI,OAAO,aAAa;;;;;;;oGAE7C,OAAO,YAAY,EAAE,+BACpB,8OAAC;wGAAE,WAAU;;4GAAsC;4GAC7B,OAAO,YAAY,CAAC,aAAa;4GAAC;;;;;;;;;;;;;;;;;;;kGAK9D,8OAAC;wFAAK,WAAU;kGACb,OAAO,KAAK,KAAK,IAAI,UAAU,GAAG,OAAO,KAAK,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;mEAnBpD,OAAO,EAAE;;;;;;;;;;;;;;;oDA4B1B,iBAAiB,OAAO,CAAC,yBAAyB,kBACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;0EAAc;;;;;;0EACtD,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;gEACtD,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAKhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM,eAAe;gEAAI,WAAU;;oEAAc;kFAEhE,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGpC,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC,0IAAA,CAAA,aAAU;wDAAC,OAAO;wDAAuB,eAAe;kEACvD,cAAA,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,cAAc,CAC7B,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,EAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAC,uBACJ,8OAAC;oEAAoB,WAAU;;sFAC7B,8OAAC,0IAAA,CAAA,iBAAc;4EAAC,OAAO,OAAO,EAAE;4EAAE,IAAI,OAAO,EAAE;;;;;;sFAC/C,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAS,OAAO,EAAE;4EAAE,WAAU;sFACnC,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;kGACtB,8OAAC;;0GACC,8OAAC;gGAAE,WAAU;0GAA2B,OAAO,IAAI;;;;;;0GACnD,8OAAC;gGAAE,WAAU;0GACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;mEARnB,OAAO,EAAE;;;;;;;;;;;;;;;oDAmB1B,CAAC;wDACA,MAAM,iBAAiB,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wDAC1E,IAAI,gBAAgB,QAAQ,kBAAkB;4DAC5C,qBACE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA0B;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFACZ,eAAe,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA,sBAC1C,8OAAC,kJAAA,CAAA,mBAAgB;gFAEf,OAAO;gFACP,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC;gFACzB,UAAU,CAAC,QAAU,eAAe,MAAM,EAAE,EAAE;+EAHzC,MAAM,EAAE;;;;;;;;;;;;;;;;wDASzB;wDACA,OAAO;oDACT,CAAC;kEAED,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAS,IAAM,eAAe;gEAAI,WAAU;;oEAAc;kFAEhE,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQ9B,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGrC,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAc;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,8OAAC;gEAAI,WAAU;0EACZ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;wEAAgB,WAAU;;0FACzB,8OAAC;gFAAK,WAAU;;oFAAe,KAAK,IAAI;oFAAC;oFAAI,KAAK,QAAQ;;;;;;;0FAC1D,8OAAC;;oFAAM,KAAK,KAAK;oFAAC;;;;;;;;uEAFV;;;;;;;;;;;;;;;;oDASf,iBAAiB,OAAO,CAAC,sBAAsB,kBAC9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS;gEACT,iBAAiB;;;;;;0EAEnB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;;oEAAsB;kFAC3C,8OAAC;wEAAE,MAAK;wEAAS,WAAU;kFAAgC;;;;;;oEAAmB;kFACxF,8OAAC;wEAAE,MAAK;wEAAW,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAKnE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,SAAS,IAAM,eAAe;gEAAI,WAAU;;kFACpE,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGzC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS;gEACT,UAAU,AAAC,iBAAiB,OAAO,CAAC,sBAAsB,IAAI,CAAC,gBAAiB;gEAChF,WAAU;;oEAET,UAAU,qBAAqB;kFAChC,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASlC,iBAAiB,OAAO,CAAC,gBAAgB,kBACxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAc;;;;;;;;;;;sDAErC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACZ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAK,WAAU;;wEAAe,KAAK,IAAI;wEAAC;wEAAI,KAAK,QAAQ;;;;;;;8EAC1D,8OAAC;;wEAAM,KAAK,KAAK;wEAAC;;;;;;;;2DAFV;;;;;;;;;;8DAOd,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,8OAAC;;wEAAM,aAAa,QAAQ;wEAAC;;;;;;;;;;;;;sEAE/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,8OAAC;;wEAAM,aAAa,GAAG;wEAAC;;;;;;;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAc;;;;;;8EAC9B,8OAAC;;wEAAM,aAAa,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;;8DAIjC,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,8OAAC;;gEAAM,aAAa,KAAK;gEAAC;;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD", "debugId": null}}]}