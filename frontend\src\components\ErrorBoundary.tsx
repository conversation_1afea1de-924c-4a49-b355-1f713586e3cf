'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  private handleReload = () => {
    window.location.reload()
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="flex flex-col items-center justify-center p-6 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400 mb-4" />
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
            حدث خطأ في تحميل القائمة
          </h3>
          <p className="text-sm text-red-600 dark:text-red-300 mb-4 text-center">
            عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleRetry}
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              إعادة المحاولة
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={this.handleReload}
              className="bg-red-600 hover:bg-red-700"
            >
              إعادة تحميل الصفحة
            </Button>
          </div>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mt-4 w-full">
              <summary className="text-xs text-red-500 cursor-pointer">
                تفاصيل الخطأ (وضع التطوير)
              </summary>
              <pre className="mt-2 text-xs text-red-600 bg-red-100 dark:bg-red-900/40 p-2 rounded overflow-auto">
                {this.state.error.stack}
              </pre>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

// مكون مبسط للقائمة في حالة الخطأ
export function NavigationFallback() {
  return (
    <nav className="flex items-center gap-4" role="navigation" aria-label="القائمة الاحتياطية">
      <a href="/" className="text-blue-600 hover:text-blue-800 font-medium">
        الرئيسية
      </a>
      <a href="/catalog" className="text-gray-700 hover:text-blue-600 font-medium">
        الكتالوج
      </a>
      <a href="/about" className="text-gray-700 hover:text-blue-600 font-medium">
        من نحن
      </a>
      <a href="/contact" className="text-gray-700 hover:text-blue-600 font-medium">
        اتصل بنا
      </a>
    </nav>
  )
}
