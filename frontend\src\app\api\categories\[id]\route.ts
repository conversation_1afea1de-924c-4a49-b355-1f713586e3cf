import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockCategory } from '@/lib/mockData'

// GET - جلب فئة واحدة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categories = MockDataManager.getCategories()
    const category = categories.find(c => c.id === params.id)

    if (!category) {
      return NextResponse.json(
        { error: 'الفئة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({ category })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث فئة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      name_ar,
      name_en,
      name_fr,
      slug,
      icon,
      description,
      is_active,
      order_index
    } = body

    // جلب الفئات الحالية
    const categories = MockDataManager.getCategories()
    const categoryIndex = categories.findIndex(c => c.id === params.id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'الفئة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار الـ slug (إذا تم تغييره)
    if (slug && slug !== categories[categoryIndex].slug) {
      const existingCategory = categories.find(c => c.slug === slug && c.id !== params.id)
      if (existingCategory) {
        return NextResponse.json(
          { error: 'الرابط المختصر موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث الفئة
    const updatedCategory: MockCategory = {
      ...categories[categoryIndex],
      name_ar: name_ar || categories[categoryIndex].name_ar,
      name_en: name_en !== undefined ? name_en : categories[categoryIndex].name_en,
      name_fr: name_fr !== undefined ? name_fr : categories[categoryIndex].name_fr,
      slug: slug || categories[categoryIndex].slug,
      icon: icon !== undefined ? icon : categories[categoryIndex].icon,
      description: description !== undefined ? description : categories[categoryIndex].description,
      is_active: is_active !== undefined ? is_active : categories[categoryIndex].is_active,
      order_index: order_index !== undefined ? order_index : categories[categoryIndex].order_index,
      updated_at: new Date().toISOString()
    }

    categories[categoryIndex] = updatedCategory
    MockDataManager.saveCategories(categories)

    return NextResponse.json({ 
      message: 'تم تحديث الفئة بنجاح',
      category: updatedCategory 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف فئة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب الفئات الحالية
    const categories = MockDataManager.getCategories()
    const categoryIndex = categories.findIndex(c => c.id === params.id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'الفئة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من وجود منتجات تستخدم هذه الفئة
    const products = MockDataManager.getProducts()
    const categorySlug = categories[categoryIndex].slug
    const productsUsingCategory = products.filter(p => p.category === categorySlug)
    
    if (productsUsingCategory.length > 0) {
      return NextResponse.json(
        { error: `لا يمكن حذف الفئة لأنها مستخدمة في ${productsUsingCategory.length} منتج` },
        { status: 400 }
      )
    }

    // حذف الفئة
    categories.splice(categoryIndex, 1)
    MockDataManager.saveCategories(categories)

    return NextResponse.json({ 
      message: 'تم حذف الفئة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
