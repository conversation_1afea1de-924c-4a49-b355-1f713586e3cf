# دليل تشغيل الخادم - Server Startup Guide

## المشكلة المحلولة

تم حل مشكلة `EADDRINUSE: address already in use :::3000` التي كانت تمنع تشغيل الخادم.

## طرق تشغيل الخادم

### الطريقة 1: استخدام NPM Scripts (الموصى بها)

```bash
# تشغيل على المنفذ 3000 (افتراضي)
npm run dev

# تشغيل على المنفذ 3005 (بديل)
npm run dev:3005

# تشغيل على المنفذ 3004 (بديل آخر)
npm run dev:3004
```

### الطريقة 2: استخدام Next.js مباشرة

```bash
# تشغيل على منفذ مخصص
npx next dev -p 3005

# تشغيل بدون Turbopack (في حالة وجود مشاكل)
npx next dev -p 3005 --no-turbopack
```

### الطريقة 3: استخدام الملف المبسط

```bash
# تشغيل الملف المبسط (Windows)
start-server.bat
```

## حل مشكلة المنفذ المستخدم

### 1. إيقاف جميع عمليات Node.js

```bash
# Windows
taskkill /f /im node.exe

# macOS/Linux
killall node
```

### 2. البحث عن العمليات المستخدمة للمنفذ

```bash
# Windows
netstat -ano | findstr :3000

# macOS/Linux
lsof -ti:3000
```

### 3. إيقاف عملية محددة

```bash
# Windows (استبدل PID برقم العملية)
taskkill /f /pid [PID]

# macOS/Linux
kill -9 [PID]
```

## التحقق من تشغيل الخادم

بعد تشغيل الخادم بنجاح، ستظهر رسالة مشابهة لهذه:

```
▲ Next.js 14.0.0
- Local:        http://localhost:3005
- Network:      http://*************:3005

✓ Ready in 2.3s
```

## الروابط المتاحة

بعد تشغيل الخادم، يمكنك الوصول للتطبيق عبر:

- **الصفحة الرئيسية**: http://localhost:3005
- **الكتالوج**: http://localhost:3005/catalog
- **لوحة التحكم**: http://localhost:3005/dashboard/admin
- **إدارة المنتجات**: http://localhost:3005/dashboard/admin/products
- **نماذج الذكاء الاصطناعي**: http://localhost:3005/dashboard/admin/ai-models

## اختبار الميزات الجديدة

### 1. اختبار نظام الصور المحسن

- انتقل إلى `/catalog` لرؤية الصور المحسنة
- تحقق من عدم وجود أخطاء 404 في الصور
- اختبر Lazy Loading عند التمرير

### 2. اختبار ميزات الذكاء الاصطناعي

- انتقل إلى `/dashboard/admin/products`
- اضغط "إضافة منتج جديد"
- انتقل إلى تبويب "الذكاء الاصطناعي"
- جرب الميزات المختلفة (توليد الوصف، العنوان، إلخ)

### 3. اختبار رفع الصور المحسن

- في نموذج إضافة المنتج
- انتقل إلى تبويب "الصور"
- ارفع صور واختبر الضغط التلقائي
- جرب ميزة تحسين الصور بالذكاء الاصطناعي

## استكشاف الأخطاء

### مشكلة: الخادم لا يبدأ

**الحل:**
1. تأكد من تثبيت Node.js (الإصدار 18+ موصى به)
2. تأكد من تثبيت التبعيات: `npm install`
3. امسح cache: `npm run clean` أو `rm -rf .next`
4. جرب منفذ مختلف: `npm run dev:3005`

### مشكلة: أخطاء في الصور

**الحل:**
- تم حل هذه المشكلة بالكامل
- جميع الصور تعمل الآن عبر APIs مخصصة
- في حالة ظهور مشاكل، تحقق من console المتصفح

### مشكلة: ميزات الذكاء الاصطناعي لا تعمل

**الحل:**
1. تأكد من تفعيل نموذج ذكاء اصطناعي في `/dashboard/admin/ai-models`
2. تحقق من console المتصفح للأخطاء
3. تأكد من أن API endpoints تعمل بشكل صحيح

## معلومات إضافية

### البيئة المطلوبة

- **Node.js**: 18.0.0 أو أحدث
- **NPM**: 8.0.0 أو أحدث
- **المتصفح**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+

### الملفات المهمة

- `package.json` - إعدادات المشروع والتبعيات
- `next.config.js` - إعدادات Next.js
- `start-server.bat` - ملف تشغيل مبسط للـ Windows

### الدعم

في حالة واجهت أي مشاكل:

1. تحقق من console المتصفح للأخطاء
2. تحقق من terminal للرسائل
3. تأكد من أن جميع التبعيات مثبتة
4. جرب إعادة تشغيل الخادم

---

## الخلاصة

تم حل مشكلة تشغيل الخادم بنجاح وإضافة عدة طرق بديلة للتشغيل. النظام الآن جاهز للاستخدام مع جميع الميزات الجديدة:

✅ **نظام صور محسن** بدون أخطاء 404  
✅ **ميزات ذكاء اصطناعي متكاملة** لإدارة المنتجات  
✅ **رفع صور محسن** مع ضغط وتحسين تلقائي  
✅ **واجهة مستخدم محسنة** مع تجربة سلسة  

استخدم أي من الطرق المذكورة أعلاه لتشغيل الخادم والاستمتاع بالميزات الجديدة! 🚀
