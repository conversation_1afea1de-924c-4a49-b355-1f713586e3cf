import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { jest } from '@jest/globals'
import Home from '@/app/page'
import { HeroSection } from '../HeroSection'
import { StatsSection } from '../StatsSection'
import { FeaturesShowcase } from '../FeaturesShowcase'
import { ProductsPreview } from '../ProductsPreview'
import { TestimonialsSection } from '../TestimonialsSection'
import { AIFeaturesSection } from '../AIFeaturesSection'
import { CTASection } from '../CTASection'

// Mock the hooks
jest.mock('@/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'home.hero.title': 'أول منصة مغربية ذكية لأزياء التخرج',
        'home.hero.subtitle': 'اكتشف مجموعة واسعة من أزياء التخرج الأنيقة',
        'home.hero.cta.primary': 'ابدأ رحلتك الآن',
        'home.hero.cta.secondary': 'اكتشف المجموعة',
        'home.stats.title': 'أرقام تتحدث عن نفسها',
        'home.stats.customers.label': 'عميل راضٍ',
        'home.stats.schools.label': 'مدرسة وجامعة',
        'home.features.title': 'ميزات تجعلنا الأفضل',
        'home.products.title': 'مجموعة مختارة من أفضل التصاميم',
        'home.testimonials.title': 'ماذا يقول عملاؤنا',
        'home.ai.title': 'قوة الذكاء الاصطناعي في خدمتك',
        'home.cta.title': 'جاهز لبدء رحلتك؟'
      }
      return translations[key] || key
    }
  })
}))

jest.mock('@/hooks/useStats', () => ({
  useStats: () => ({
    stats: {
      customers: 1200,
      schools: 50,
      orders: 2500,
      satisfaction: 98
    },
    loading: false,
    error: null,
    refetch: jest.fn()
  })
}))

jest.mock('@/hooks/useProducts', () => ({
  useProducts: () => ({
    products: [],
    featuredProducts: [
      {
        id: '1',
        name: 'ثوب التخرج الكلاسيكي',
        price: '450 Dhs',
        rating: 5,
        reviews: 24,
        category: 'أثواب التخرج',
        isFeatured: true
      }
    ],
    loading: false,
    error: null,
    refetch: jest.fn()
  })
}))

jest.mock('@/hooks/useTestimonials', () => ({
  useTestimonials: () => ({
    testimonials: [
      {
        id: '1',
        name: 'أمينة الحسني',
        role: 'طالبة ماجستير',
        university: 'جامعة محمد الخامس',
        content: 'تجربة رائعة!',
        rating: 5,
        avatar: '/avatars/amina.jpg',
        date: 'منذ أسبوعين'
      }
    ],
    loading: false,
    error: null,
    refetch: jest.fn()
  })
}))

// Mock Next.js components
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  )
})

jest.mock('@/components/layouts/PageLayout', () => {
  return ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-layout">{children}</div>
  )
})

describe('Homepage Components', () => {
  describe('Home Page', () => {
    it('renders all main sections', () => {
      render(<Home />)
      
      expect(screen.getByTestId('page-layout')).toBeInTheDocument()
    })
  })

  describe('HeroSection', () => {
    it('renders hero content correctly', async () => {
      render(<HeroSection />)
      
      await waitFor(() => {
        expect(screen.getByText('أول منصة مغربية ذكية لأزياء التخرج')).toBeInTheDocument()
        expect(screen.getByText('ابدأ رحلتك الآن')).toBeInTheDocument()
        expect(screen.getByText('اكتشف المجموعة')).toBeInTheDocument()
      })
    })

    it('has proper CTA buttons with correct links', () => {
      render(<HeroSection />)
      
      const primaryCTA = screen.getByRole('link', { name: /ابدأ رحلتك الآن/ })
      const secondaryCTA = screen.getByRole('link', { name: /اكتشف المجموعة/ })
      
      expect(primaryCTA).toHaveAttribute('href', '/customize')
      expect(secondaryCTA).toHaveAttribute('href', '/catalog')
    })
  })

  describe('StatsSection', () => {
    it('displays stats correctly', async () => {
      render(<StatsSection />)
      
      await waitFor(() => {
        expect(screen.getByText('أرقام تتحدث عن نفسها')).toBeInTheDocument()
        expect(screen.getByText('عميل راضٍ')).toBeInTheDocument()
        expect(screen.getByText('مدرسة وجامعة')).toBeInTheDocument()
      })
    })

    it('shows loading state when data is loading', () => {
      // This would require mocking the hook to return loading: true
      render(<StatsSection />)
      // Add assertions for loading state
    })
  })

  describe('FeaturesShowcase', () => {
    it('renders features grid', () => {
      render(<FeaturesShowcase />)
      
      expect(screen.getByText('ميزات تجعلنا الأفضل')).toBeInTheDocument()
    })

    it('has interactive feature cards', () => {
      render(<FeaturesShowcase />)
      
      const featureCards = screen.getAllByText('اعرف المزيد')
      expect(featureCards.length).toBeGreaterThan(0)
    })
  })

  describe('ProductsPreview', () => {
    it('displays featured products', async () => {
      render(<ProductsPreview />)
      
      await waitFor(() => {
        expect(screen.getByText('مجموعة مختارة من أفضل التصاميم')).toBeInTheDocument()
      })
    })

    it('has product interaction buttons', () => {
      render(<ProductsPreview />)
      
      // Check for category filter buttons
      const categoryButtons = screen.getAllByRole('button')
      expect(categoryButtons.length).toBeGreaterThan(0)
    })
  })

  describe('TestimonialsSection', () => {
    it('displays customer testimonials', async () => {
      render(<TestimonialsSection />)
      
      await waitFor(() => {
        expect(screen.getByText('ماذا يقول عملاؤنا')).toBeInTheDocument()
        expect(screen.getByText('أمينة الحسني')).toBeInTheDocument()
      })
    })

    it('has navigation controls', () => {
      render(<TestimonialsSection />)
      
      const navigationButtons = screen.getAllByRole('button')
      const arrowButtons = navigationButtons.filter(btn => 
        btn.querySelector('svg') // Looking for arrow icons
      )
      expect(arrowButtons.length).toBeGreaterThanOrEqual(2)
    })
  })

  describe('AIFeaturesSection', () => {
    it('displays AI features and models', () => {
      render(<AIFeaturesSection />)
      
      expect(screen.getByText('قوة الذكاء الاصطناعي في خدمتك')).toBeInTheDocument()
    })

    it('has interactive AI demo buttons', () => {
      render(<AIFeaturesSection />)
      
      const demoButtons = screen.getAllByText('جرب الآن')
      expect(demoButtons.length).toBeGreaterThan(0)
    })
  })

  describe('CTASection', () => {
    it('renders call-to-action content', () => {
      render(<CTASection />)
      
      expect(screen.getByText('جاهز لبدء رحلتك؟')).toBeInTheDocument()
    })

    it('has proper CTA buttons', () => {
      render(<CTASection />)
      
      const ctaButtons = screen.getAllByRole('link')
      expect(ctaButtons.length).toBeGreaterThanOrEqual(2)
    })
  })
})

describe('Accessibility Tests', () => {
  it('has proper heading hierarchy', () => {
    render(<Home />)
    
    // Check for proper heading structure
    const headings = screen.getAllByRole('heading')
    expect(headings.length).toBeGreaterThan(0)
  })

  it('has proper alt text for images', () => {
    render(<Home />)
    
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      expect(img).toHaveAttribute('alt')
    })
  })

  it('has proper ARIA labels for interactive elements', () => {
    render(<Home />)
    
    const buttons = screen.getAllByRole('button')
    const links = screen.getAllByRole('link')
    
    // Check that interactive elements have proper labels
    [...buttons, ...links].forEach(element => {
      expect(
        element.hasAttribute('aria-label') || 
        element.textContent?.trim() !== ''
      ).toBe(true)
    })
  })
})

describe('Performance Tests', () => {
  it('renders without performance issues', async () => {
    const startTime = performance.now()
    render(<Home />)
    const endTime = performance.now()
    
    // Should render within reasonable time (less than 100ms)
    expect(endTime - startTime).toBeLessThan(100)
  })

  it('handles intersection observer properly', () => {
    // Mock IntersectionObserver
    const mockIntersectionObserver = jest.fn()
    mockIntersectionObserver.mockReturnValue({
      observe: () => null,
      unobserve: () => null,
      disconnect: () => null
    })
    
    window.IntersectionObserver = mockIntersectionObserver
    
    render(<StatsSection />)
    
    expect(mockIntersectionObserver).toHaveBeenCalled()
  })
})

describe('Responsive Design Tests', () => {
  it('adapts to mobile viewport', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    })
    
    render(<Home />)
    
    // Check that mobile-specific classes are applied
    // This would require checking for responsive classes in the DOM
  })

  it('adapts to tablet viewport', () => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    })
    
    render(<Home />)
    
    // Check tablet-specific layout
  })
})
