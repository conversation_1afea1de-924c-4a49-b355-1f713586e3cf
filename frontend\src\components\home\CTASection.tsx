"use client"

import { useState, useEffect, useRef } from 'react'
import { useTranslation } from "@/hooks/useTranslation"
import { Button } from "@/components/ui/button"
import { 
  ArrowRight, 
  Sparkles, 
  MessageSquare,
  GraduationCap,
  Star,
  Users
} from "lucide-react"

export function CTASection() {
  const { t } = useTranslation()
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section 
      ref={ref}
      className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border border-white rounded-full animate-pulse" />
        <div className="absolute top-32 right-20 w-16 h-16 border border-white rounded-full animate-bounce" />
        <div className="absolute bottom-20 left-32 w-24 h-24 border border-white rounded-full animate-pulse" />
        <div className="absolute bottom-32 right-10 w-12 h-12 border border-white rounded-full animate-bounce" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main Content */}
          <div className={`transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6">
              <GraduationCap className="w-10 h-10 text-white" />
            </div>

            {/* Title */}
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 arabic-text leading-tight">
              {t('home.cta.title')}
            </h2>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-blue-100 mb-4 arabic-text">
              {t('home.cta.subtitle')}
            </p>

            {/* Description */}
            <p className="text-lg text-blue-200 mb-12 max-w-2xl mx-auto arabic-text leading-relaxed">
              {t('home.cta.description')}
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Users className="w-6 h-6 text-blue-200" />
                  <span className="text-3xl font-bold text-white">1,200+</span>
                </div>
                <p className="text-blue-200 arabic-text">طالب راضٍ</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Star className="w-6 h-6 text-yellow-300" />
                  <span className="text-3xl font-bold text-white">4.9</span>
                </div>
                <p className="text-blue-200 arabic-text">تقييم العملاء</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <GraduationCap className="w-6 h-6 text-blue-200" />
                  <span className="text-3xl font-bold text-white">50+</span>
                </div>
                <p className="text-blue-200 arabic-text">مدرسة وجامعة</p>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group"
                asChild
              >
                <a href="/customize" className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  {t('home.cta.button')}
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </a>
              </Button>
              
              <Button 
                variant="outline" 
                size="lg" 
                className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300"
                asChild
              >
                <a href="/contact" className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  {t('home.cta.contact')}
                </a>
              </Button>
            </div>
          </div>

          {/* Additional Info */}
          <div className={`mt-16 transition-all duration-1000 delay-500 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-2xl font-bold text-white mb-2">✅</div>
                  <p className="text-blue-100 arabic-text">ضمان الجودة</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white mb-2">🚚</div>
                  <p className="text-blue-100 arabic-text">توصيل مجاني</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white mb-2">🔄</div>
                  <p className="text-blue-100 arabic-text">إرجاع مجاني</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 dark:from-gray-900 to-transparent" />
    </section>
  )
}
