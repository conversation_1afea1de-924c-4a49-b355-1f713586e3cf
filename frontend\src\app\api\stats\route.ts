import { NextRequest, NextResponse } from 'next/server'
import { ProductModel } from '@/lib/models/Product'
import { UserModel } from '@/lib/models/User'
import { OrderModel } from '@/lib/models/Order'
import { checkDatabaseHealth } from '@/lib/database'

// GET - جلب إحصائيات شاملة للمنصة
export async function GET(request: NextRequest) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json(
        { error: 'قاعدة البيانات غير متاحة' },
        { status: 503 }
      )
    }

    // جلب إحصائيات المستخدمين
    const userStats = await UserModel.getStats()
    
    // جلب إحصائيات الطلبات
    const orderStats = await OrderModel.getStats()
    
    // جلب المنتجات الأكثر مبيعاً
    const bestSellers = await ProductModel.getBestSellers(5)
    
    // جلب المنتجات الجديدة
    const newProducts = await ProductModel.getNewProducts(5)
    
    // جلب المنتجات ذات التقييم العالي
    const topRated = await ProductModel.getTopRated(5)

    // حساب إحصائيات إضافية
    const totalProducts = await ProductModel.getAll({ limit: 1 })
    const publishedProducts = await ProductModel.getAll({ published: true, limit: 1 })
    const availableProducts = await ProductModel.getAll({ available: true, limit: 1 })

    const stats = {
      // إحصائيات المستخدمين
      users: {
        total: userStats.total,
        active: userStats.active,
        verified: userStats.verified,
        byRole: userStats.byRole,
        growthRate: calculateGrowthRate(userStats.total, userStats.active)
      },
      
      // إحصائيات الطلبات
      orders: {
        total: orderStats.total,
        pending: orderStats.pending,
        confirmed: orderStats.confirmed,
        delivered: orderStats.delivered,
        cancelled: orderStats.cancelled,
        totalRevenue: orderStats.totalRevenue,
        averageOrderValue: orderStats.averageOrderValue,
        conversionRate: userStats.total > 0 ? (orderStats.total / userStats.total * 100) : 0
      },
      
      // إحصائيات المنتجات
      products: {
        total: totalProducts.total,
        published: publishedProducts.total,
        available: availableProducts.total,
        publishedRate: totalProducts.total > 0 ? (publishedProducts.total / totalProducts.total * 100) : 0,
        availabilityRate: totalProducts.total > 0 ? (availableProducts.total / totalProducts.total * 100) : 0
      },
      
      // المنتجات المميزة
      featured: {
        bestSellers: bestSellers.slice(0, 5),
        newProducts: newProducts.slice(0, 5),
        topRated: topRated.slice(0, 5)
      },
      
      // إحصائيات عامة
      overview: {
        totalRevenue: orderStats.totalRevenue,
        totalOrders: orderStats.total,
        totalUsers: userStats.total,
        totalProducts: totalProducts.total,
        averageOrderValue: orderStats.averageOrderValue,
        customerSatisfaction: calculateCustomerSatisfaction(topRated),
        platformHealth: calculatePlatformHealth(userStats, orderStats, totalProducts.total)
      },
      
      // معلومات إضافية
      metadata: {
        lastUpdated: new Date().toISOString(),
        source: 'postgresql',
        version: '1.0.0'
      }
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { error: 'فشل في جلب الإحصائيات' },
      { status: 500 }
    )
  }
}

// دالة لحساب معدل النمو
function calculateGrowthRate(total: number, active: number): number {
  if (total === 0) return 0
  return Math.round((active / total) * 100)
}

// دالة لحساب رضا العملاء
function calculateCustomerSatisfaction(topRated: any[]): number {
  if (topRated.length === 0) return 0
  const averageRating = topRated.reduce((sum, product) => sum + (product.rating || 0), 0) / topRated.length
  return Math.round((averageRating / 5) * 100)
}

// دالة لحساب صحة المنصة
function calculatePlatformHealth(userStats: any, orderStats: any, totalProducts: number): number {
  const factors = [
    userStats.active / Math.max(userStats.total, 1), // نسبة المستخدمين النشطين
    orderStats.delivered / Math.max(orderStats.total, 1), // نسبة الطلبات المكتملة
    (orderStats.total - orderStats.cancelled) / Math.max(orderStats.total, 1), // نسبة الطلبات الناجحة
    Math.min(totalProducts / 100, 1) // نسبة توفر المنتجات (حد أقصى 100)
  ]
  
  const averageHealth = factors.reduce((sum, factor) => sum + factor, 0) / factors.length
  return Math.round(averageHealth * 100)
}
