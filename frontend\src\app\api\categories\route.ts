import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockCategory } from '@/lib/mockData'

// GET - جلب جميع الفئات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('include_inactive') === 'true'

    // جلب البيانات الوهمية
    let categories = MockDataManager.getCategories()

    // تطبيق الفلاتر
    if (!includeInactive) {
      categories = categories.filter(category => category.is_active)
    }

    // ترتيب حسب order_index
    categories.sort((a, b) => a.order_index - b.order_index)

    return NextResponse.json({ 
      categories,
      total: categories.length
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة فئة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name_ar,
      name_en,
      name_fr,
      slug,
      icon,
      description,
      is_active,
      order_index
    } = body

    // التحقق من البيانات المطلوبة
    if (!name_ar || !slug) {
      return NextResponse.json(
        { error: 'الاسم العربي والرابط المختصر مطلوبان' },
        { status: 400 }
      )
    }

    // جلب الفئات الحالية
    const categories = MockDataManager.getCategories()

    // التحقق من عدم تكرار الـ slug
    const existingCategory = categories.find(category => category.slug === slug)
    if (existingCategory) {
      return NextResponse.json(
        { error: 'الرابط المختصر موجود بالفعل' },
        { status: 400 }
      )
    }

    // إنشاء الفئة الجديدة
    const newCategory: MockCategory = {
      id: MockDataManager.generateId(),
      name_ar,
      name_en: name_en || undefined,
      name_fr: name_fr || undefined,
      slug,
      icon: icon || undefined,
      description: description || undefined,
      is_active: is_active ?? true,
      order_index: order_index || categories.length + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // حفظ الفئة
    categories.push(newCategory)
    MockDataManager.saveCategories(categories)

    return NextResponse.json({ 
      message: 'تم إضافة الفئة بنجاح',
      category: newCategory 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
