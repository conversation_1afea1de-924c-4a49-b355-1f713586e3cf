'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { toast } from 'sonner'

// تعريف نوع عنصر القائمة
export interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  created_at: string
  updated_at: string
}

// تعريف نوع السياق
interface MenuContextType {
  menuItems: MenuItem[]
  loading: boolean
  error: string | null
  fetchMenuItems: (includeInactive?: boolean) => Promise<void>
  addMenuItem: (item: Omit<MenuItem, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>
  updateMenuItem: (id: string, updates: Partial<MenuItem>) => Promise<boolean>
  deleteMenuItem: (id: string) => Promise<boolean>
  toggleItemStatus: (id: string) => Promise<boolean>
  reorderMenuItems: (items: MenuItem[]) => Promise<boolean>
  refreshMenu: () => void
}

// إنشاء السياق
const MenuContext = createContext<MenuContextType | undefined>(undefined)

// مزود السياق
export function MenuProvider({ children }: { children: ReactNode }) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // جلب عناصر القائمة
  const fetchMenuItems = async (includeInactive: boolean = false) => {
    try {
      setLoading(true)
      setError(null)
      
      const url = includeInactive ? '/api/menu-items?include_inactive=true' : '/api/menu-items'
      const response = await fetch(url)
      const data = await response.json()
      
      if (response.ok) {
        setMenuItems(data.menuItems || [])
      } else {
        setError(data.error || 'فشل في جلب عناصر القائمة')
        console.error('Failed to fetch menu items:', data.error)
      }
    } catch (error) {
      const errorMessage = 'خطأ في الاتصال بالخادم'
      setError(errorMessage)
      console.error('Error fetching menu items:', error)
    } finally {
      setLoading(false)
    }
  }

  // إضافة عنصر قائمة جديد
  const addMenuItem = async (item: Omit<MenuItem, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
    try {
      const response = await fetch('/api/menu-items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item),
      })

      const data = await response.json()

      if (response.ok) {
        // تحديث القائمة محلياً
        setMenuItems(prev => [...prev, data.menuItem])
        toast.success(data.message)
        return true
      } else {
        toast.error(data.error || 'فشل في إضافة عنصر القائمة')
        return false
      }
    } catch (error) {
      console.error('Error adding menu item:', error)
      toast.error('خطأ في الاتصال بالخادم')
      return false
    }
  }

  // تحديث عنصر قائمة
  const updateMenuItem = async (id: string, updates: Partial<MenuItem>): Promise<boolean> => {
    try {
      const currentItem = menuItems.find(item => item.id === id)
      if (!currentItem) {
        toast.error('عنصر القائمة غير موجود')
        return false
      }

      const updatedItem = { ...currentItem, ...updates }
      
      const response = await fetch(`/api/menu-items/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedItem),
      })

      const data = await response.json()

      if (response.ok) {
        // تحديث القائمة محلياً
        setMenuItems(prev => prev.map(item => 
          item.id === id ? { ...item, ...updates } : item
        ))
        toast.success(data.message)
        return true
      } else {
        toast.error(data.error || 'فشل في تحديث عنصر القائمة')
        return false
      }
    } catch (error) {
      console.error('Error updating menu item:', error)
      toast.error('خطأ في الاتصال بالخادم')
      return false
    }
  }

  // حذف عنصر قائمة
  const deleteMenuItem = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/menu-items/${id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (response.ok) {
        // حذف العنصر محلياً
        setMenuItems(prev => prev.filter(item => item.id !== id))
        toast.success(data.message)
        return true
      } else {
        toast.error(data.error || 'فشل في حذف عنصر القائمة')
        return false
      }
    } catch (error) {
      console.error('Error deleting menu item:', error)
      toast.error('خطأ في الاتصال بالخادم')
      return false
    }
  }

  // تبديل حالة التفعيل
  const toggleItemStatus = async (id: string): Promise<boolean> => {
    const item = menuItems.find(item => item.id === id)
    if (!item) {
      toast.error('عنصر القائمة غير موجود')
      return false
    }

    return await updateMenuItem(id, { is_active: !item.is_active })
  }

  // إعادة ترتيب عناصر القائمة
  const reorderMenuItems = async (items: MenuItem[]): Promise<boolean> => {
    try {
      // تحديث الحالة المحلية أولاً للاستجابة السريعة
      setMenuItems(items)

      const itemsToUpdate = items
        .filter(item => !item.parent_id) // العناصر الرئيسية فقط
        .map((item, index) => ({
          id: item.id,
          order_index: index + 1
        }))

      const response = await fetch('/api/menu-items/reorder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items: itemsToUpdate }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        return true
      } else {
        // في حالة الفشل، إعادة جلب البيانات من الخادم
        await fetchMenuItems()
        toast.error(data.error || 'فشل في إعادة ترتيب عناصر القائمة')
        return false
      }
    } catch (error) {
      // في حالة الخطأ، إعادة جلب البيانات من الخادم
      await fetchMenuItems()
      console.error('Error reordering menu items:', error)
      toast.error('خطأ في الاتصال بالخادم')
      return false
    }
  }

  // تحديث القائمة
  const refreshMenu = () => {
    fetchMenuItems()
  }

  // جلب البيانات عند التحميل الأول
  useEffect(() => {
    fetchMenuItems()
  }, [])

  const value: MenuContextType = {
    menuItems,
    loading,
    error,
    fetchMenuItems,
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    toggleItemStatus,
    reorderMenuItems,
    refreshMenu
  }

  return (
    <MenuContext.Provider value={value}>
      {children}
    </MenuContext.Provider>
  )
}

// Hook لاستخدام السياق
export function useMenu() {
  const context = useContext(MenuContext)
  if (context === undefined) {
    throw new Error('useMenu must be used within a MenuProvider')
  }
  return context
}
