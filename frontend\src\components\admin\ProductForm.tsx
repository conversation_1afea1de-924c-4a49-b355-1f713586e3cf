"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  Upload,
  X,
  Plus,
  Image as ImageIcon,
  Save,
  Eye,
  AlertCircle,
  Package,
  Palette,
  Ruler,
  DollarSign,
  Star,
  Brain
} from 'lucide-react'
import { ImageUploader } from './ImageUploader'
import { AIProductEnhancer } from './AIProductEnhancer'

// أنواع البيانات
interface Category {
  id: string
  name_ar: string
  name_en?: string
  slug: string
  icon?: string
  is_active: boolean
}

interface ProductFormData {
  name: string
  description: string
  category: string
  price: number
  rental_price?: number
  colors: string[]
  sizes: string[]
  images: { file: File; preview: string; id: string }[]
  stock_quantity: number
  is_available: boolean
  is_published: boolean
  features: string[]
  specifications: { [key: string]: string }
}

interface ProductFormProps {
  onSubmit: (data: ProductFormData) => void
  onCancel: () => void
  initialData?: Partial<ProductFormData>
  isEditing?: boolean
}

const predefinedColors = [
  'أسود', 'أزرق داكن', 'بورجوندي', 'ذهبي', 'فضي', 'أبيض', 
  'أحمر', 'أخضر', 'بنفسجي', 'وردي', 'برتقالي', 'بني'
]

const predefinedSizes = [
  'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'واحد'
]

export function ProductForm({
  onSubmit,
  onCancel,
  initialData = {},
  isEditing = false
}: ProductFormProps) {
  const [formData, setFormData] = useState<ProductFormData>({
    name: initialData.name || '',
    description: initialData.description || '',
    category: initialData.category || '',
    price: initialData.price || 0,
    rental_price: initialData.rental_price || 0,
    colors: initialData.colors || [],
    sizes: initialData.sizes || [],
    images: initialData.images || [],
    stock_quantity: initialData.stock_quantity || 0,
    is_available: initialData.is_available ?? true,
    is_published: initialData.is_published ?? true,
    features: initialData.features || [],
    specifications: initialData.specifications || {}
  })

  const [categories, setCategories] = useState<Category[]>([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [newColor, setNewColor] = useState('')
  const [newSize, setNewSize] = useState('')
  const [newFeature, setNewFeature] = useState('')
  const [newSpecKey, setNewSpecKey] = useState('')
  const [newSpecValue, setNewSpecValue] = useState('')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  // جلب الفئات
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data.categories)
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      } finally {
        setLoadingCategories(false)
      }
    }

    fetchCategories()
  }, [])

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المنتج مطلوب'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المنتج مطلوب'
    }

    if (!formData.category) {
      newErrors.category = 'فئة المنتج مطلوبة'
    }

    if (formData.price <= 0) {
      newErrors.price = 'السعر يجب أن يكون أكبر من صفر'
    }

    if (formData.colors.length === 0) {
      newErrors.colors = 'يجب إضافة لون واحد على الأقل'
    }

    if (formData.sizes.length === 0) {
      newErrors.sizes = 'يجب إضافة مقاس واحد على الأقل'
    }

    if (formData.stock_quantity < 0) {
      newErrors.stock_quantity = 'كمية المخزون لا يمكن أن تكون سالبة'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // إضافة لون جديد
  const addColor = () => {
    if (newColor.trim() && !formData.colors.includes(newColor.trim())) {
      setFormData(prev => ({
        ...prev,
        colors: [...prev.colors, newColor.trim()]
      }))
      setNewColor('')
    }
  }

  // حذف لون
  const removeColor = (color: string) => {
    setFormData(prev => ({
      ...prev,
      colors: prev.colors.filter(c => c !== color)
    }))
  }

  // إضافة مقاس جديد
  const addSize = () => {
    if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {
      setFormData(prev => ({
        ...prev,
        sizes: [...prev.sizes, newSize.trim()]
      }))
      setNewSize('')
    }
  }

  // حذف مقاس
  const removeSize = (size: string) => {
    setFormData(prev => ({
      ...prev,
      sizes: prev.sizes.filter(s => s !== size)
    }))
  }

  // إضافة ميزة جديدة
  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature('')
    }
  }

  // حذف ميزة
  const removeFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }))
  }

  // إضافة مواصفة جديدة
  const addSpecification = () => {
    if (newSpecKey.trim() && newSpecValue.trim()) {
      setFormData(prev => ({
        ...prev,
        specifications: {
          ...prev.specifications,
          [newSpecKey.trim()]: newSpecValue.trim()
        }
      }))
      setNewSpecKey('')
      setNewSpecValue('')
    }
  }

  // حذف مواصفة
  const removeSpecification = (key: string) => {
    setFormData(prev => {
      const newSpecs = { ...prev.specifications }
      delete newSpecs[key]
      return {
        ...prev,
        specifications: newSpecs
      }
    })
  }

  // تحديث البيانات من الذكاء الاصطناعي
  const handleAIUpdate = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // إرسال النموذج
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic" className="arabic-text">
            <Package className="h-4 w-4 mr-2" />
            المعلومات الأساسية
          </TabsTrigger>
          <TabsTrigger value="details" className="arabic-text">
            <Palette className="h-4 w-4 mr-2" />
            التفاصيل والألوان
          </TabsTrigger>
          <TabsTrigger value="images" className="arabic-text">
            <ImageIcon className="h-4 w-4 mr-2" />
            الصور
          </TabsTrigger>
          <TabsTrigger value="features" className="arabic-text">
            <Star className="h-4 w-4 mr-2" />
            المميزات والمواصفات
          </TabsTrigger>
          <TabsTrigger value="ai" className="arabic-text">
            <Brain className="h-4 w-4 mr-2" />
            الذكاء الاصطناعي
          </TabsTrigger>
        </TabsList>

        {/* المعلومات الأساسية */}
        <TabsContent value="basic" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* اسم المنتج */}
            <div className="space-y-2">
              <Label htmlFor="name" className="arabic-text">اسم المنتج *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم المنتج"
                className="arabic-text"
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* فئة المنتج */}
            <div className="space-y-2">
              <Label htmlFor="category" className="arabic-text">فئة المنتج *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                disabled={loadingCategories}
              >
                <SelectTrigger>
                  <SelectValue placeholder={loadingCategories ? "جاري تحميل الفئات..." : "اختر فئة المنتج"} />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.slug} value={category.slug}>
                      <div className="flex items-center gap-2">
                        {category.icon && <span>{category.icon}</span>}
                        <span className="arabic-text">{category.name_ar}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.category}
                </p>
              )}
            </div>
          </div>

          {/* وصف المنتج */}
          <div className="space-y-2">
            <Label htmlFor="description" className="arabic-text">وصف المنتج *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="أدخل وصف مفصل للمنتج"
              className="arabic-text min-h-[100px]"
            />
            {errors.description && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.description}
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* السعر */}
            <div className="space-y-2">
              <Label htmlFor="price" className="arabic-text">السعر (درهم) *</Label>
              <Input
                id="price"
                type="number"
                min="0"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
              />
              {errors.price && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.price}
                </p>
              )}
            </div>

            {/* سعر الإيجار */}
            <div className="space-y-2">
              <Label htmlFor="rental_price" className="arabic-text">سعر الإيجار (درهم)</Label>
              <Input
                id="rental_price"
                type="number"
                min="0"
                step="0.01"
                value={formData.rental_price}
                onChange={(e) => setFormData(prev => ({ ...prev, rental_price: parseFloat(e.target.value) || 0 }))}
                placeholder="0.00"
              />
            </div>

            {/* كمية المخزون */}
            <div className="space-y-2">
              <Label htmlFor="stock_quantity" className="arabic-text">كمية المخزون *</Label>
              <Input
                id="stock_quantity"
                type="number"
                min="0"
                value={formData.stock_quantity}
                onChange={(e) => setFormData(prev => ({ ...prev, stock_quantity: parseInt(e.target.value) || 0 }))}
                placeholder="0"
              />
              {errors.stock_quantity && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.stock_quantity}
                </p>
              )}
            </div>
          </div>

          {/* متاح للبيع */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_available"
              checked={formData.is_available}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_available: checked }))}
            />
            <Label htmlFor="is_available" className="arabic-text">
              متاح للبيع
            </Label>
          </div>

          {/* نشر المنتج */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_published"
              checked={formData.is_published}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_published: checked }))}
            />
            <Label htmlFor="is_published" className="arabic-text">
              نشر المنتج في الكتالوج
            </Label>
          </div>
        </TabsContent>

        {/* التفاصيل والألوان */}
        <TabsContent value="details" className="space-y-6 mt-6">
          {/* الألوان */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">الألوان المتاحة</CardTitle>
              <CardDescription className="arabic-text">
                أضف الألوان المتاحة للمنتج
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* إضافة لون جديد */}
              <div className="flex gap-2">
                <Select value={newColor} onValueChange={setNewColor}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="اختر لون" />
                  </SelectTrigger>
                  <SelectContent>
                    {predefinedColors.map(color => (
                      <SelectItem key={color} value={color}>{color}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  value={newColor}
                  onChange={(e) => setNewColor(e.target.value)}
                  placeholder="أو أدخل لون مخصص"
                  className="flex-1 arabic-text"
                />
                <Button type="button" onClick={addColor} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* عرض الألوان المضافة */}
              <div className="flex flex-wrap gap-2">
                {formData.colors.map((color, index) => (
                  <Badge key={index} variant="secondary" className="arabic-text">
                    {color}
                    <button
                      type="button"
                      onClick={() => removeColor(color)}
                      className="ml-2 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>

              {errors.colors && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.colors}
                </p>
              )}
            </CardContent>
          </Card>

          {/* المقاسات */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">المقاسات المتاحة</CardTitle>
              <CardDescription className="arabic-text">
                أضف المقاسات المتاحة للمنتج
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* إضافة مقاس جديد */}
              <div className="flex gap-2">
                <Select value={newSize} onValueChange={setNewSize}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="اختر مقاس" />
                  </SelectTrigger>
                  <SelectContent>
                    {predefinedSizes.map(size => (
                      <SelectItem key={size} value={size}>{size}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  value={newSize}
                  onChange={(e) => setNewSize(e.target.value)}
                  placeholder="أو أدخل مقاس مخصص"
                  className="flex-1 arabic-text"
                />
                <Button type="button" onClick={addSize} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* عرض المقاسات المضافة */}
              <div className="flex flex-wrap gap-2">
                {formData.sizes.map((size, index) => (
                  <Badge key={index} variant="secondary" className="arabic-text">
                    {size}
                    <button
                      type="button"
                      onClick={() => removeSize(size)}
                      className="ml-2 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>

              {errors.sizes && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.sizes}
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* الصور */}
        <TabsContent value="images" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">صور المنتج</CardTitle>
              <CardDescription className="arabic-text">
                أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUploader
                images={formData.images}
                onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
                maxImages={8}
                maxSize={5 * 1024 * 1024} // 5MB
                acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* المميزات والمواصفات */}
        <TabsContent value="features" className="space-y-6 mt-6">
          {/* المميزات */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">مميزات المنتج</CardTitle>
              <CardDescription className="arabic-text">
                أضف المميزات الرئيسية للمنتج
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* إضافة ميزة جديدة */}
              <div className="flex gap-2">
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="أدخل ميزة جديدة"
                  className="flex-1 arabic-text"
                />
                <Button type="button" onClick={addFeature} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {/* عرض المميزات */}
              <div className="space-y-2">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <span className="arabic-text">{feature}</span>
                    <button
                      type="button"
                      onClick={() => removeFeature(feature)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>

              {formData.features.length === 0 && (
                <p className="text-gray-500 text-center py-4 arabic-text">
                  لم يتم إضافة أي مميزات بعد
                </p>
              )}
            </CardContent>
          </Card>

          {/* المواصفات */}
          <Card>
            <CardHeader>
              <CardTitle className="arabic-text">مواصفات المنتج</CardTitle>
              <CardDescription className="arabic-text">
                أضف المواصفات التقنية للمنتج
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* إضافة مواصفة جديدة */}
              <div className="grid grid-cols-2 gap-2">
                <Input
                  value={newSpecKey}
                  onChange={(e) => setNewSpecKey(e.target.value)}
                  placeholder="اسم المواصفة (مثل: المادة)"
                  className="arabic-text"
                />
                <div className="flex gap-2">
                  <Input
                    value={newSpecValue}
                    onChange={(e) => setNewSpecValue(e.target.value)}
                    placeholder="قيمة المواصفة"
                    className="flex-1 arabic-text"
                  />
                  <Button type="button" onClick={addSpecification} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* عرض المواصفات */}
              <div className="space-y-2">
                {Object.entries(formData.specifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="arabic-text">
                      <span className="font-medium">{key}:</span> {value}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeSpecification(key)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>

              {Object.keys(formData.specifications).length === 0 && (
                <p className="text-gray-500 text-center py-4 arabic-text">
                  لم يتم إضافة أي مواصفات بعد
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* الذكاء الاصطناعي */}
        <TabsContent value="ai" className="space-y-6 mt-6">
          <AIProductEnhancer
            productData={{
              name: formData.name,
              description: formData.description,
              category: formData.category,
              price: formData.price,
              features: formData.features,
              specifications: formData.specifications
            }}
            onUpdate={handleAIUpdate}
          />
        </TabsContent>
      </Tabs>

      {/* أزرار الإجراءات */}
      <div className="flex justify-end gap-4 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          إلغاء
        </Button>
        <Button type="button" variant="outline">
          <Eye className="h-4 w-4 mr-2" />
          معاينة
        </Button>
        <Button type="submit">
          <Save className="h-4 w-4 mr-2" />
          {isEditing ? 'تحديث المنتج' : 'إضافة المنتج'}
        </Button>
      </div>
    </form>
  )
}
