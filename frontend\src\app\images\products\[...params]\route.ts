import { NextRequest, NextResponse } from 'next/server'

// قاموس الصور المتاحة مع أوصافها
const productImages: { [key: string]: { name: string; category: string; color: string } } = {
  'gown-classic-1.jpg': { name: 'ثوب التخرج الكلاسيكي', category: 'gown', color: 'أسود' },
  'gown-classic-2.jpg': { name: 'ثوب التخرج الكلاسيكي', category: 'gown', color: 'أزرق داكن' },
  'cap-traditional-1.jpg': { name: 'قبعة التخرج التقليدية', category: 'cap', color: 'أسود' },
  'cap-premium-1.jpg': { name: 'قبعة التخرج المميزة', category: 'cap', color: 'ذهبي' },
  'stole-embroidered-1.jpg': { name: 'وشاح التخرج المطرز', category: 'stole', color: 'أزرق' },
  'stole-silk-1.jpg': { name: 'وشاح التخرج الحريري', category: 'stole', color: 'أحمر' },
  'tassel-gold-1.jpg': { name: 'شرابة التخرج الذهبية', category: 'tassel', color: 'ذهبي' },
  'tassel-silver-1.jpg': { name: 'شرابة التخرج الفضية', category: 'tassel', color: 'فضي' },
  'hood-doctoral-1.jpg': { name: 'قلنسوة الدكتوراه', category: 'hood', color: 'أزرق' },
  'accessories-set-1.jpg': { name: 'طقم إكسسوارات التخرج', category: 'accessories', color: 'متعدد' }
}

// ألوان الفئات
const categoryColors: { [key: string]: string } = {
  'gown': '#1a1a1a',      // أسود
  'cap': '#2c3e50',       // أزرق داكن
  'stole': '#8e44ad',     // بنفسجي
  'tassel': '#f39c12',    // ذهبي
  'hood': '#27ae60',      // أخضر
  'accessories': '#e74c3c' // أحمر
}

// GET - إنشاء صور منتجات ديناميكية
export async function GET(
  request: NextRequest,
  { params }: { params: { params: string[] } }
) {
  try {
    const filename = params.params.join('/')
    const imageInfo = productImages[filename]
    
    if (!imageInfo) {
      // إنشاء صورة افتراضية للمنتجات غير المعروفة
      return generateDefaultProductImage(filename)
    }
    
    const categoryColor = categoryColors[imageInfo.category] || '#666666'
    
    // إنشاء SVG للمنتج
    const svg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="productGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${categoryColor};stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:${categoryColor};stop-opacity:0.6" />
          </linearGradient>
        </defs>
        
        <!-- الخلفية -->
        <rect width="100%" height="100%" fill="url(#bgGrad)"/>
        
        <!-- إطار المنتج -->
        <rect x="50" y="40" width="300" height="220" 
              fill="url(#productGrad)" 
              stroke="${categoryColor}" 
              stroke-width="2" 
              rx="10"/>
        
        <!-- أيقونة المنتج حسب الفئة -->
        ${generateCategoryIcon(imageInfo.category, categoryColor)}
        
        <!-- اسم المنتج -->
        <text x="200" y="280" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="14" 
              font-weight="bold"
              fill="#333">
          ${imageInfo.name}
        </text>
        
        <!-- اللون -->
        <text x="200" y="295" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="12" 
              fill="#666">
          ${imageInfo.color}
        </text>
        
        <!-- شعار الجودة -->
        <circle cx="350" cy="70" r="20" fill="#28a745" opacity="0.9"/>
        <text x="350" y="75" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="10" 
              font-weight="bold"
              fill="white">
          HD
        </text>
      </svg>
    `
    
    return new NextResponse(svg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=86400', // يوم واحد
      },
    })
    
  } catch (error) {
    console.error('Error generating product image:', error)
    return generateDefaultProductImage('unknown')
  }
}

function generateCategoryIcon(category: string, color: string): string {
  const iconColor = '#ffffff'
  
  switch (category) {
    case 'gown':
      return `
        <g transform="translate(180, 120)">
          <!-- ثوب التخرج -->
          <path d="M10 20 L10 60 L40 60 L40 20 L35 15 L15 15 Z" 
                fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <rect x="15" y="15" width="20" height="8" fill="${iconColor}"/>
          <line x1="20" y1="25" x2="20" y2="55" stroke="${color}" stroke-width="1"/>
          <line x1="30" y1="25" x2="30" y2="55" stroke="${color}" stroke-width="1"/>
        </g>
      `
    case 'cap':
      return `
        <g transform="translate(180, 130)">
          <!-- قبعة التخرج -->
          <ellipse cx="20" cy="25" rx="18" ry="8" fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <rect x="15" y="15" width="10" height="15" fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <rect x="10" y="20" width="20" height="3" fill="${iconColor}"/>
          <!-- الشرابة -->
          <line x1="35" y1="20" x2="40" y2="30" stroke="${color}" stroke-width="2"/>
          <circle cx="40" cy="32" r="2" fill="${color}"/>
        </g>
      `
    case 'stole':
      return `
        <g transform="translate(180, 120)">
          <!-- وشاح التخرج -->
          <path d="M5 10 Q20 5 35 10 L35 50 Q20 55 5 50 Z" 
                fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <path d="M8 15 Q20 12 32 15" stroke="${color}" stroke-width="1" fill="none"/>
          <path d="M8 25 Q20 22 32 25" stroke="${color}" stroke-width="1" fill="none"/>
          <path d="M8 35 Q20 32 32 35" stroke="${color}" stroke-width="1" fill="none"/>
        </g>
      `
    case 'tassel':
      return `
        <g transform="translate(190, 120)">
          <!-- شرابة التخرج -->
          <rect x="8" y="10" width="4" height="15" fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <circle cx="10" cy="8" r="3" fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <!-- الخيوط -->
          <line x1="6" y1="25" x2="6" y2="40" stroke="${color}" stroke-width="1"/>
          <line x1="8" y1="25" x2="8" y2="42" stroke="${color}" stroke-width="1"/>
          <line x1="10" y1="25" x2="10" y2="41" stroke="${color}" stroke-width="1"/>
          <line x1="12" y1="25" x2="12" y2="43" stroke="${color}" stroke-width="1"/>
          <line x1="14" y1="25" x2="14" y2="40" stroke="${color}" stroke-width="1"/>
        </g>
      `
    case 'hood':
      return `
        <g transform="translate(180, 120)">
          <!-- قلنسوة -->
          <path d="M10 20 Q25 10 40 20 L40 40 Q25 50 10 40 Z" 
                fill="${iconColor}" stroke="${color}" stroke-width="1"/>
          <path d="M15 25 Q25 20 35 25" stroke="${color}" stroke-width="1" fill="none"/>
          <circle cx="20" cy="30" r="2" fill="${color}"/>
          <circle cx="30" cy="30" r="2" fill="${color}"/>
        </g>
      `
    default:
      return `
        <g transform="translate(180, 120)">
          <!-- أيقونة افتراضية -->
          <rect x="10" y="15" width="20" height="25" 
                fill="${iconColor}" stroke="${color}" stroke-width="1" rx="3"/>
          <circle cx="20" cy="27" r="5" fill="${color}"/>
          <text x="20" y="31" text-anchor="middle" font-size="8" fill="white">?</text>
        </g>
      `
  }
}

function generateDefaultProductImage(filename: string): NextResponse {
  const svg = `
    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f8f9fa"/>
      <rect x="20" y="20" width="360" height="260" 
            fill="none" stroke="#dee2e6" stroke-width="2" stroke-dasharray="10,5" rx="10"/>
      
      <!-- أيقونة صورة مفقودة -->
      <g transform="translate(170, 120)">
        <rect x="10" y="15" width="40" height="30" 
              fill="none" stroke="#6c757d" stroke-width="2" rx="3"/>
        <circle cx="20" cy="25" r="3" fill="#6c757d"/>
        <polygon points="15,35 25,28 35,32 45,22 45,40 15,40" fill="#6c757d"/>
        <line x1="10" y1="10" x2="50" y2="50" stroke="#dc3545" stroke-width="3"/>
      </g>
      
      <text x="200" y="200" 
            text-anchor="middle" 
            font-family="Arial, sans-serif" 
            font-size="14" 
            fill="#6c757d">
        صورة غير متوفرة
      </text>
      
      <text x="200" y="220" 
            text-anchor="middle" 
            font-family="Arial, sans-serif" 
            font-size="12" 
            fill="#adb5bd">
        ${filename}
      </text>
    </svg>
  `
  
  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=3600',
    },
  })
}
