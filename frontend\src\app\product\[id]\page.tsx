"use client"

import { useState } from 'react'
import { useParams } from 'next/navigation'
import { useTranslation } from '@/hooks/useTranslation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { UserMenu } from '@/components/auth/UserMenu'
import { 
  GraduationCap, 
  Heart,
  ShoppingCart,
  Star,
  ArrowLeft,
  Share2,
  Truck,
  Shield,
  RotateCcw
} from 'lucide-react'
import Link from 'next/link'

// بيانات تجريبية للمنتج
const sampleProduct = {
  id: 1,
  name: 'ثوب التخرج الكلاسيكي الفاخر',
  description: 'ثوب تخرج أنيق ومريح للمناسبات الرسمية، مصنوع من أجود الخامات المستوردة مع تفاصيل دقيقة وتصميم عصري يجمع بين الأناقة والراحة.',
  category: 'gown',
  price: 299.99,
  rental_price: 99.99,
  colors: [
    { name: 'أسود', value: 'black', hex: '#000000' },
    { name: 'أزرق داكن', value: 'navy', hex: '#1e3a8a' },
    { name: 'بورجوندي', value: 'burgundy', hex: '#7c2d12' }
  ],
  sizes: ['S', 'M', 'L', 'XL', 'XXL'],
  images: [
    '/images/products/gown-classic-1.jpg',
    '/images/products/gown-classic-2.jpg',
    '/api/placeholder/500/600',
    '/api/placeholder/500/600'
  ],
  rating: 4.8,
  reviews: 124,
  isNew: true,
  features: [
    'خامة عالية الجودة',
    'تصميم مريح وأنيق',
    'مقاوم للتجاعيد',
    'سهل العناية والغسيل',
    'متوفر بمقاسات متعددة'
  ],
  specifications: {
    'المادة': 'بوليستر عالي الجودة',
    'الوزن': '450 جرام',
    'العناية': 'غسيل جاف أو غسيل عادي',
    'المنشأ': 'تركيا',
    'الضمان': 'سنة واحدة'
  }
}

export default function ProductPage() {
  const params = useParams()
  const { t } = useTranslation()
  const [selectedColor, setSelectedColor] = useState(sampleProduct.colors[0])
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedImage, setSelectedImage] = useState(0)
  const [isFavorite, setIsFavorite] = useState(false)
  const [orderType, setOrderType] = useState<'rental' | 'purchase'>('rental')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link href="/catalog">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للكتالوج
              </Button>
            </Link>
            <div className="flex items-center gap-2">
              <GraduationCap className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white arabic-text">
                تفاصيل المنتج
              </h1>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <LanguageToggle />
            <ThemeToggle />
            <UserMenu />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative">
              <img
                src={sampleProduct.images[selectedImage]}
                alt={sampleProduct.name}
                className="w-full h-96 object-cover rounded-lg shadow-lg"
              />
              {sampleProduct.isNew && (
                <Badge className="absolute top-4 right-4 bg-green-500">
                  جديد
                </Badge>
              )}
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className="absolute top-4 left-4 p-2 bg-white dark:bg-gray-800 rounded-full shadow-md hover:scale-110 transition-transform"
              >
                <Heart 
                  className={`h-5 w-5 ${
                    isFavorite 
                      ? 'text-red-500 fill-current' 
                      : 'text-gray-400'
                  }`} 
                />
              </button>
            </div>
            
            {/* Thumbnail Images */}
            <div className="flex gap-2 overflow-x-auto">
              {sampleProduct.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index 
                      ? 'border-blue-500' 
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${sampleProduct.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            {/* Rating */}
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(sampleProduct.rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-lg font-semibold">{sampleProduct.rating}</span>
              <span className="text-gray-600 dark:text-gray-400">
                ({sampleProduct.reviews} تقييم)
              </span>
            </div>

            {/* Product Name */}
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
              {sampleProduct.name}
            </h1>

            {/* Description */}
            <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed arabic-text">
              {sampleProduct.description}
            </p>

            {/* Price */}
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant={orderType === 'rental' ? 'default' : 'outline'}
                  onClick={() => setOrderType('rental')}
                  className="arabic-text"
                >
                  إيجار
                </Button>
                <Button
                  variant={orderType === 'purchase' ? 'default' : 'outline'}
                  onClick={() => setOrderType('purchase')}
                  className="arabic-text"
                >
                  شراء
                </Button>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {orderType === 'rental' ? sampleProduct.rental_price : sampleProduct.price} درهم
                </div>
                {orderType === 'rental' && (
                  <div className="text-lg text-gray-500 line-through">
                    {sampleProduct.price} درهم
                  </div>
                )}
              </div>
            </div>

            {/* Color Selection */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold arabic-text">اللون:</h3>
              <div className="flex gap-3">
                {sampleProduct.colors.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => setSelectedColor(color)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border-2 transition-colors ${
                      selectedColor.value === color.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div 
                      className="w-6 h-6 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: color.hex }}
                    />
                    <span className="arabic-text">{color.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Size Selection */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold arabic-text">المقاس:</h3>
              <Select value={selectedSize} onValueChange={setSelectedSize}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="اختر المقاس" />
                </SelectTrigger>
                <SelectContent>
                  {sampleProduct.sizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button 
                size="lg" 
                className="flex-1 arabic-text"
                disabled={!selectedSize}
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                إضافة للسلة
              </Button>
              <Button size="lg" variant="outline">
                <Share2 className="h-5 w-5" />
              </Button>
            </div>

            {/* Features */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold arabic-text">المميزات:</h3>
              <div className="grid grid-cols-1 gap-2">
                {sampleProduct.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span className="arabic-text">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Service Icons */}
            <div className="grid grid-cols-3 gap-4 pt-6 border-t">
              <div className="text-center">
                <Truck className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <p className="text-sm arabic-text">توصيل مجاني</p>
              </div>
              <div className="text-center">
                <Shield className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-sm arabic-text">ضمان الجودة</p>
              </div>
              <div className="text-center">
                <RotateCcw className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                <p className="text-sm arabic-text">إرجاع مجاني</p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-12">
          <Tabs defaultValue="specifications" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="specifications" className="arabic-text">المواصفات</TabsTrigger>
              <TabsTrigger value="reviews" className="arabic-text">التقييمات</TabsTrigger>
              <TabsTrigger value="care" className="arabic-text">العناية</TabsTrigger>
            </TabsList>
            
            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">مواصفات المنتج</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(sampleProduct.specifications).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b">
                        <span className="font-semibold arabic-text">{key}:</span>
                        <span className="arabic-text">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="reviews" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تقييمات العملاء</CardTitle>
                  <CardDescription className="arabic-text">
                    شاهد ما يقوله عملاؤنا عن هذا المنتج
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-gray-500 py-8 arabic-text">
                    سيتم إضافة التقييمات قريباً...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="care" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تعليمات العناية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 arabic-text">
                    <div>
                      <h4 className="font-semibold mb-2">الغسيل:</h4>
                      <p>يُفضل الغسيل الجاف أو الغسيل بالماء البارد</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">التجفيف:</h4>
                      <p>تجفيف في الهواء الطلق بعيداً عن أشعة الشمس المباشرة</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">الكي:</h4>
                      <p>كي على درجة حرارة منخفضة مع استخدام قطعة قماش واقية</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}
