"use client"

import { useState, useEffect, useRef } from 'react'
import { useTranslation } from "@/hooks/useTranslation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Palette, 
  Sparkles, 
  Users, 
  GraduationCap,
  Shield,
  Headphones,
  ArrowRight,
  CheckCircle
} from "lucide-react"

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  color: string
  delay: number
  features?: string[]
}

function FeatureCard({ icon, title, description, color, delay, features }: FeatureCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2">
        <CardHeader className="text-center pb-4">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300`}>
            {icon}
          </div>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <CardDescription className="text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4">
            {description}
          </CardDescription>
          
          {features && (
            <div className="space-y-2 mb-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span className="arabic-text">{feature}</span>
                </div>
              ))}
            </div>
          )}
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn"
          >
            اعرف المزيد
            <ArrowRight className="w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform" />
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

export function FeaturesShowcase() {
  const { t } = useTranslation()

  const features = [
    {
      icon: <Palette className="w-8 h-8 text-white" />,
      title: t('home.features.customization.title'),
      description: t('home.features.customization.description'),
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      delay: 0,
      features: [
        "معاينة فورية ثلاثية الأبعاد",
        "مكتبة ألوان واسعة",
        "إكسسوارات متنوعة"
      ]
    },
    {
      icon: <Sparkles className="w-8 h-8 text-white" />,
      title: t('home.features.ai.title'),
      description: t('home.features.ai.description'),
      color: "bg-gradient-to-br from-yellow-500 to-yellow-600",
      delay: 200,
      features: [
        "اقتراحات ذكية مخصصة",
        "تحليل الأسلوب الشخصي",
        "مساعد افتراضي متقدم"
      ]
    },
    {
      icon: <Users className="w-8 h-8 text-white" />,
      title: t('home.features.roles.title'),
      description: t('home.features.roles.description'),
      color: "bg-gradient-to-br from-green-500 to-green-600",
      delay: 400,
      features: [
        "لوحة تحكم للطلاب",
        "إدارة المدارس",
        "نظام شركاء التوصيل"
      ]
    },
    {
      icon: <GraduationCap className="w-8 h-8 text-white" />,
      title: t('home.features.tracking.title'),
      description: t('home.features.tracking.description'),
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      delay: 600,
      features: [
        "تتبع لحظي للطلبات",
        "إشعارات فورية",
        "تحديثات مستمرة"
      ]
    },
    {
      icon: <Shield className="w-8 h-8 text-white" />,
      title: t('home.features.quality.title'),
      description: t('home.features.quality.description'),
      color: "bg-gradient-to-br from-red-500 to-red-600",
      delay: 800,
      features: [
        "أقمشة فاخرة مختارة",
        "تصنيع احترافي",
        "ضمان الجودة"
      ]
    },
    {
      icon: <Headphones className="w-8 h-8 text-white" />,
      title: t('home.features.support.title'),
      description: t('home.features.support.description'),
      color: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      delay: 1000,
      features: [
        "دعم على مدار الساعة",
        "فريق متخصص",
        "استجابة سريعة"
      ]
    }
  ]

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Sparkles className="w-4 h-4" />
            ميزاتنا المتقدمة
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.features.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text">
            {t('home.features.subtitle')}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              color={feature.color}
              delay={feature.delay}
              features={feature.features}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
