import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockPage } from '@/lib/mockData'

// GET - جلب صفحة واحدة مع محتواها
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language') || 'ar'

    // جلب البيانات الوهمية
    const pages = MockDataManager.getPages()
    const page = pages.find(p => p.id === params.id)

    if (!page) {
      return NextResponse.json(
        { error: 'الصفحة غير موجودة' },
        { status: 404 }
      )
    }

    // فلترة المحتوى حسب اللغة المطلوبة
    const filteredContent = page.page_content.filter(
      content => content.language === language
    )

    return NextResponse.json({ 
      page: {
        ...page,
        page_content: filteredContent
      }
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث صفحة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const {
      slug,
      is_published,
      featured_image,
      content
    } = body

    // جلب الصفحات الحالية
    const pages = MockDataManager.getPages()
    const pageIndex = pages.findIndex(p => p.id === params.id)

    if (pageIndex === -1) {
      return NextResponse.json(
        { error: 'الصفحة غير موجودة' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار الـ slug (إذا تم تغييره)
    if (slug && slug !== pages[pageIndex].slug) {
      const existingPage = pages.find(page => page.slug === slug && page.id !== params.id)
      if (existingPage) {
        return NextResponse.json(
          { error: 'الرابط المختصر موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تحديث الصفحة
    const updatedPage = {
      ...pages[pageIndex],
      slug: slug || pages[pageIndex].slug,
      is_published: is_published ?? pages[pageIndex].is_published,
      featured_image: featured_image ?? pages[pageIndex].featured_image,
      updated_at: new Date().toISOString()
    }

    // تحديث المحتوى إذا تم توفيره
    if (content) {
      const newContent = []
      const languages: ('ar' | 'en' | 'fr')[] = ['ar', 'en', 'fr']
      
      languages.forEach(lang => {
        if (content[lang]?.title) {
          newContent.push({
            id: `${params.id}-${lang}`,
            page_id: params.id,
            language: lang,
            title: content[lang].title,
            content: content[lang].content || '',
            meta_description: content[lang].meta_description || '',
            meta_keywords: content[lang].meta_keywords || ''
          })
        }
      })
      
      updatedPage.page_content = newContent
    }

    // حفظ التحديثات
    pages[pageIndex] = updatedPage
    MockDataManager.savePages(pages)

    return NextResponse.json({ 
      message: 'تم تحديث الصفحة بنجاح',
      page: updatedPage 
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف صفحة
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // جلب الصفحات الحالية
    const pages = MockDataManager.getPages()
    const pageIndex = pages.findIndex(p => p.id === params.id)

    if (pageIndex === -1) {
      return NextResponse.json(
        { error: 'الصفحة غير موجودة' },
        { status: 404 }
      )
    }

    // حذف الصفحة
    pages.splice(pageIndex, 1)
    MockDataManager.savePages(pages)

    return NextResponse.json({ 
      message: 'تم حذف الصفحة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
