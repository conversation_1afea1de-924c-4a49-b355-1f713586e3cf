import { NextRequest, NextResponse } from 'next/server'
import { OrderModel } from '@/lib/models/Order'
import { checkDatabaseHealth } from '@/lib/database'

// GET - جلب جميع الطلبات من PostgreSQL
export async function GET(request: NextRequest) {
  try {
    // التحقق من حالة قاعدة البيانات
    const isHealthy = await checkDatabaseHealth()
    if (!isHealthy) {
      return NextResponse.json(
        { error: 'قاعدة البيانات غير متاحة' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    
    // استخراج معاملات الفلترة
    const filters = {
      status: searchParams.get('status') || undefined,
      user_id: searchParams.get('user_id') || undefined,
      school_id: searchParams.get('school_id') || undefined,
      payment_status: searchParams.get('payment_status') || undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
      sortBy: (searchParams.get('sortBy') as any) || 'created_at',
      sortOrder: (searchParams.get('sortOrder') as 'ASC' | 'DESC') || 'DESC'
    }

    // جلب الطلبات من قاعدة البيانات
    const result = await OrderModel.getAll(filters)

    return NextResponse.json({
      orders: result.orders,
      total: result.total,
      page: Math.floor(filters.offset / filters.limit) + 1,
      totalPages: Math.ceil(result.total / filters.limit),
      filters: filters,
      source: 'postgresql'
    })
  } catch (error) {
    console.error('Error fetching orders from PostgreSQL:', error)
    return NextResponse.json(
      { error: 'فشل في جلب الطلبات من قاعدة البيانات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء طلب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من البيانات المطلوبة
    if (!body.user_id || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        { error: 'معرف المستخدم وعناصر الطلب مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من صحة عناصر الطلب
    for (const item of body.items) {
      if (!item.product_id || !item.quantity || !item.unit_price) {
        return NextResponse.json(
          { error: 'معرف المنتج والكمية والسعر مطلوبة لكل عنصر' },
          { status: 400 }
        )
      }
    }

    // إنشاء الطلب
    const order = await OrderModel.create({
      user_id: body.user_id,
      school_id: body.school_id,
      items: body.items,
      shipping_address: body.shipping_address,
      billing_address: body.billing_address,
      payment_method: body.payment_method,
      notes: body.notes
    })

    return NextResponse.json({
      message: 'تم إنشاء الطلب بنجاح',
      order: order
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: 'فشل في إنشاء الطلب' },
      { status: 500 }
    )
  }
}
