import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'

// PUT - تحديث ترتيب عناصر القائمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { items } = body

    if (!items || !Array.isArray(items)) {
      return NextResponse.json(
        { error: 'قائمة العناصر مطلوبة' },
        { status: 400 }
      )
    }

    // جلب العناصر الحالية
    const menuItems = MockDataManager.getMenuItems()

    // تحديث ترتيب العناصر
    items.forEach((item: { id: string; order_index: number }, index: number) => {
      const menuItemIndex = menuItems.findIndex(menuItem => menuItem.id === item.id)
      if (menuItemIndex !== -1) {
        menuItems[menuItemIndex].order_index = index + 1
        menuItems[menuItemIndex].updated_at = new Date().toISOString()
      }
    })

    // حفظ التحديثات
    MockDataManager.saveMenuItems(menuItems)

    return NextResponse.json({ 
      message: 'تم تحديث ترتيب القائمة بنجاح'
    })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
