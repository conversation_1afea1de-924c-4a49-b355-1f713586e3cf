'use client'

import React from 'react'

interface NavigationSkeletonProps {
  isMobile?: boolean
}

export function NavigationSkeleton({ isMobile = false }: NavigationSkeletonProps) {
  const skeletonItems = Array.from({ length: 5 }, (_, i) => i)

  if (isMobile) {
    return (
      <div className="flex flex-col gap-1 mb-6">
        {skeletonItems.map((index) => (
          <div
            key={index}
            className="flex items-center gap-3 px-4 py-3 mx-2 rounded-xl animate-pulse"
            style={{
              animationDelay: `${index * 100}ms`
            }}
          >
            {/* Icon skeleton */}
            <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer" />
            
            {/* Text skeleton */}
            <div className="flex-1">
              <div 
                className="h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer"
                style={{ width: `${60 + Math.random() * 40}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <nav className="hidden lg:flex items-center gap-1">
      {skeletonItems.map((index) => (
        <div
          key={index}
          className="flex items-center gap-2 px-4 py-2.5 rounded-xl animate-pulse"
          style={{
            animationDelay: `${index * 100}ms`
          }}
        >
          {/* Icon skeleton */}
          <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer" />
          
          {/* Text skeleton */}
          <div 
            className="h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer"
            style={{ width: `${50 + Math.random() * 30}px` }}
          />
        </div>
      ))}
    </nav>
  )
}

// Skeleton للعناصر الجانبية (Cart, Wishlist, etc.)
export function SideElementsSkeleton() {
  return (
    <div className="flex items-center gap-3">
      {/* Search skeleton */}
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse" />
      
      {/* Cart skeleton */}
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse" style={{ animationDelay: '100ms' }} />
      
      {/* Wishlist skeleton */}
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse" style={{ animationDelay: '200ms' }} />
      
      {/* Theme toggle skeleton */}
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse" style={{ animationDelay: '300ms' }} />
      
      {/* Language skeleton */}
      <div className="w-16 h-8 bg-gray-300 dark:bg-gray-600 rounded shimmer animate-pulse" style={{ animationDelay: '400ms' }} />
      
      {/* Profile skeleton */}
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse" style={{ animationDelay: '500ms' }} />
    </div>
  )
}
