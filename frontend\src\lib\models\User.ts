import { query, transaction } from '../database'
import { PoolClient } from 'pg'
import bcrypt from 'bcryptjs'

export interface User {
  id: string
  email: string
  password_hash: string
  first_name: string
  last_name: string
  phone?: string
  role: 'admin' | 'customer' | 'school' | 'delivery'
  is_active: boolean
  email_verified: boolean
  profile_image?: string
  created_at: Date
  updated_at: Date
}

export interface UserProfile {
  id: string
  email: string
  first_name: string
  last_name: string
  phone?: string
  role: string
  is_active: boolean
  email_verified: boolean
  profile_image?: string
  created_at: Date
  updated_at: Date
}

export interface CreateUserData {
  email: string
  password: string
  first_name: string
  last_name: string
  phone?: string
  role?: 'admin' | 'customer' | 'school' | 'delivery'
}

export interface UpdateUserData {
  first_name?: string
  last_name?: string
  phone?: string
  profile_image?: string
  is_active?: boolean
  email_verified?: boolean
}

export class UserModel {
  // إنشاء مستخدم جديد
  static async create(userData: CreateUserData): Promise<UserProfile> {
    // تشفير كلمة المرور
    const saltRounds = 12
    const password_hash = await bcrypt.hash(userData.password, saltRounds)

    const result = await query(`
      INSERT INTO users (
        email, password_hash, first_name, last_name, phone, role
      ) VALUES (
        $1, $2, $3, $4, $5, $6
      ) RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `, [
      userData.email.toLowerCase(),
      password_hash,
      userData.first_name,
      userData.last_name,
      userData.phone,
      userData.role || 'customer'
    ])

    return result.rows[0]
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  static async findByEmail(email: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE email = $1',
      [email.toLowerCase()]
    )

    return result.rows[0] || null
  }

  // البحث عن مستخدم بالمعرف
  static async findById(id: string): Promise<UserProfile | null> {
    const result = await query(`
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users WHERE id = $1
    `, [id])

    return result.rows[0] || null
  }

  // التحقق من كلمة المرور
  static async verifyPassword(email: string, password: string): Promise<UserProfile | null> {
    const user = await this.findByEmail(email)
    if (!user) return null

    const isValid = await bcrypt.compare(password, user.password_hash)
    if (!isValid) return null

    // إرجاع بيانات المستخدم بدون كلمة المرور
    const { password_hash, ...userProfile } = user
    return userProfile as UserProfile
  }

  // تحديث بيانات المستخدم
  static async update(id: string, updates: UpdateUserData): Promise<UserProfile | null> {
    const setClause: string[] = []
    const params: any[] = []
    let paramIndex = 1

    // بناء جملة SET ديناميكياً
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        setClause.push(`${key} = $${paramIndex}`)
        params.push(value)
        paramIndex++
      }
    })

    if (setClause.length === 0) {
      throw new Error('لا توجد حقول للتحديث')
    }

    // إضافة updated_at
    setClause.push(`updated_at = NOW()`)
    params.push(id)

    const result = await query(`
      UPDATE users 
      SET ${setClause.join(', ')} 
      WHERE id = $${paramIndex} 
      RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `, params)

    return result.rows[0] || null
  }

  // تغيير كلمة المرور
  static async changePassword(id: string, newPassword: string): Promise<boolean> {
    const saltRounds = 12
    const password_hash = await bcrypt.hash(newPassword, saltRounds)

    const result = await query(
      'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
      [password_hash, id]
    )

    return result.rowCount > 0
  }

  // تفعيل البريد الإلكتروني
  static async verifyEmail(id: string): Promise<boolean> {
    const result = await query(
      'UPDATE users SET email_verified = true, updated_at = NOW() WHERE id = $1',
      [id]
    )

    return result.rowCount > 0
  }

  // تعطيل/تفعيل المستخدم
  static async toggleActive(id: string): Promise<boolean> {
    const result = await query(
      'UPDATE users SET is_active = NOT is_active, updated_at = NOW() WHERE id = $1',
      [id]
    )

    return result.rowCount > 0
  }

  // حذف المستخدم
  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM users WHERE id = $1',
      [id]
    )

    return result.rowCount > 0
  }

  // جلب جميع المستخدمين مع الفلترة
  static async getAll(filters: {
    role?: string
    is_active?: boolean
    search?: string
    limit?: number
    offset?: number
  } = {}): Promise<{ users: UserProfile[], total: number }> {
    let whereConditions: string[] = []
    let params: any[] = []
    let paramIndex = 1

    // بناء شروط WHERE
    if (filters.role) {
      whereConditions.push(`role = $${paramIndex}`)
      params.push(filters.role)
      paramIndex++
    }

    if (filters.is_active !== undefined) {
      whereConditions.push(`is_active = $${paramIndex}`)
      params.push(filters.is_active)
      paramIndex++
    }

    if (filters.search) {
      whereConditions.push(`(first_name ILIKE $${paramIndex} OR last_name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`)
      params.push(`%${filters.search}%`)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // حد النتائج والإزاحة
    const limit = filters.limit || 50
    const offset = filters.offset || 0
    const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)

    // استعلام العد الكلي
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)

    // استعلام البيانات
    const dataQuery = `
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users 
      ${whereClause} 
      ORDER BY created_at DESC 
      ${limitClause}
    `
    
    const result = await query(dataQuery, params)
    
    return {
      users: result.rows,
      total
    }
  }

  // إحصائيات المستخدمين
  static async getStats(): Promise<{
    total: number
    active: number
    verified: number
    byRole: Record<string, number>
  }> {
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_active = true) as active,
        COUNT(*) FILTER (WHERE email_verified = true) as verified
      FROM users
    `)

    const roleStatsResult = await query(`
      SELECT role, COUNT(*) as count
      FROM users
      GROUP BY role
    `)

    const byRole: Record<string, number> = {}
    roleStatsResult.rows.forEach(row => {
      byRole[row.role] = parseInt(row.count)
    })

    return {
      total: parseInt(statsResult.rows[0].total),
      active: parseInt(statsResult.rows[0].active),
      verified: parseInt(statsResult.rows[0].verified),
      byRole
    }
  }
}
