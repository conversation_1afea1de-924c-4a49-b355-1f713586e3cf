"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CreditCard,
  Building,
  Truck,
  Settings,
  Save,
  ArrowLeft,
  CheckCircle,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react'
import { toast } from 'sonner'
import Link from 'next/link'

interface PaymentMethod {
  id: string
  name: string
  type: 'credit_card' | 'bank_transfer' | 'cash_on_delivery' | 'digital_wallet'
  enabled: boolean
  configuration: {
    [key: string]: any
  }
  fees: {
    fixed_fee: number
    percentage_fee: number
  }
  description?: string
}

interface BankAccount {
  id: string
  bank_name: string
  account_holder: string
  account_number: string
  rib: string
  swift?: string
  branch?: string
  enabled: boolean
}

const defaultPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    name: 'الدفع عند الاستلام',
    type: 'cash_on_delivery',
    enabled: true,
    configuration: {
      max_amount: 2000,
      available_cities: ['بني ملال', 'خنيفرة', 'الفقيه بن صالح']
    },
    fees: {
      fixed_fee: 0,
      percentage_fee: 0
    },
    description: 'الدفع نقداً عند استلام الطلب'
  },
  {
    id: '2',
    name: 'التحويل البنكي',
    type: 'bank_transfer',
    enabled: true,
    configuration: {
      processing_time: '2-4 ساعات عمل',
      requires_proof: true
    },
    fees: {
      fixed_fee: 0,
      percentage_fee: 0
    },
    description: 'تحويل مباشر إلى الحساب البنكي'
  },
  {
    id: '3',
    name: 'البطاقة الائتمانية',
    type: 'credit_card',
    enabled: false,
    configuration: {
      supported_cards: ['Visa', 'Mastercard'],
      requires_3ds: true
    },
    fees: {
      fixed_fee: 5,
      percentage_fee: 2.5
    },
    description: 'الدفع بالبطاقة الائتمانية (قريباً)'
  }
]

const defaultBankAccounts: BankAccount[] = [
  {
    id: '1',
    bank_name: 'البنك الشعبي المغربي',
    account_holder: 'منصة أزياء التخرج المغربية',
    account_number: '****************',
    rib: '236 000 ********** 12',
    swift: 'BMCEMAMC',
    branch: 'بني ملال',
    enabled: true
  },
  {
    id: '2',
    bank_name: 'بنك المغرب',
    account_holder: 'منصة أزياء التخرج المغربية',
    account_number: '****************',
    rib: '011 000 ********** 34',
    swift: 'BMCEMAMC',
    branch: 'خنيفرة',
    enabled: true
  }
]

export default function PaymentMethodsPage() {
  const { user, profile } = useAuth()
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>(defaultPaymentMethods)
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>(defaultBankAccounts)
  const [loading, setLoading] = useState(false)
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null)
  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null)

  // التحقق من صلاحيات الأدمن
  useEffect(() => {
    if (!user || !profile || profile.role !== 'admin') {
      window.location.href = '/dashboard'
      return
    }
  }, [user, profile])

  const togglePaymentMethod = async (methodId: string) => {
    setPaymentMethods(prev => 
      prev.map(method => 
        method.id === methodId 
          ? { ...method, enabled: !method.enabled }
          : method
      )
    )
    toast.success('تم تحديث حالة طريقة الدفع')
  }

  const toggleBankAccount = async (accountId: string) => {
    setBankAccounts(prev => 
      prev.map(account => 
        account.id === accountId 
          ? { ...account, enabled: !account.enabled }
          : account
      )
    )
    toast.success('تم تحديث حالة الحساب البنكي')
  }

  const saveSettings = async () => {
    setLoading(true)
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('تم حفظ الإعدادات بنجاح')
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const getMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'credit_card':
        return <CreditCard className="h-5 w-5" />
      case 'bank_transfer':
        return <Building className="h-5 w-5" />
      case 'cash_on_delivery':
        return <Truck className="h-5 w-5" />
      default:
        return <Settings className="h-5 w-5" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للوحة التحكم
                </Link>
              </Button>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/">الصفحة الرئيسية</Link>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            إدارة طرق الدفع 💳
          </h1>
          <p className="text-gray-600 dark:text-gray-400 arabic-text">
            إدارة وتكوين طرق الدفع المتاحة للعملاء
          </p>
        </div>

        <Tabs defaultValue="methods" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="methods" className="arabic-text">
              <CreditCard className="h-4 w-4 mr-2" />
              طرق الدفع
            </TabsTrigger>
            <TabsTrigger value="bank-accounts" className="arabic-text">
              <Building className="h-4 w-4 mr-2" />
              الحسابات البنكية
            </TabsTrigger>
            <TabsTrigger value="settings" className="arabic-text">
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات العامة
            </TabsTrigger>
          </TabsList>

          {/* Payment Methods Tab */}
          <TabsContent value="methods" className="space-y-6">
            <div className="grid gap-6">
              {paymentMethods.map((method) => (
                <Card key={method.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getMethodIcon(method.type)}
                        <div>
                          <CardTitle className="arabic-text">{method.name}</CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                            {method.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={method.enabled ? "default" : "secondary"}>
                          {method.enabled ? 'مفعل' : 'معطل'}
                        </Badge>
                        <Switch
                          checked={method.enabled}
                          onCheckedChange={() => togglePaymentMethod(method.id)}
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2 arabic-text">الرسوم:</h4>
                        <div className="space-y-1 text-sm">
                          <p>رسوم ثابتة: {method.fees.fixed_fee} Dhs</p>
                          <p>رسوم نسبية: {method.fees.percentage_fee}%</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2 arabic-text">التكوين:</h4>
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          {Object.entries(method.configuration).map(([key, value]) => (
                            <p key={key}>
                              {key}: {Array.isArray(value) ? value.join(', ') : String(value)}
                            </p>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingMethod(method)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        تعديل
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Bank Accounts Tab */}
          <TabsContent value="bank-accounts" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-semibold arabic-text">الحسابات البنكية</h3>
              <Button onClick={() => setEditingAccount({
                id: '',
                bank_name: '',
                account_holder: '',
                account_number: '',
                rib: '',
                swift: '',
                branch: '',
                enabled: true
              })}>
                <Plus className="h-4 w-4 mr-2" />
                إضافة حساب جديد
              </Button>
            </div>

            <div className="grid gap-6">
              {bankAccounts.map((account) => (
                <Card key={account.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Building className="h-5 w-5 text-blue-600" />
                        <div>
                          <CardTitle className="arabic-text">{account.bank_name}</CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            الفرع: {account.branch}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={account.enabled ? "default" : "secondary"}>
                          {account.enabled ? 'مفعل' : 'معطل'}
                        </Badge>
                        <Switch
                          checked={account.enabled}
                          onCheckedChange={() => toggleBankAccount(account.id)}
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div>
                          <Label className="text-sm font-medium">صاحب الحساب:</Label>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{account.account_holder}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium">رقم الحساب:</Label>
                          <p className="text-sm font-mono text-gray-600 dark:text-gray-400">{account.account_number}</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <Label className="text-sm font-medium">RIB:</Label>
                          <p className="text-sm font-mono text-gray-600 dark:text-gray-400">{account.rib}</p>
                        </div>
                        {account.swift && (
                          <div>
                            <Label className="text-sm font-medium">SWIFT:</Label>
                            <p className="text-sm font-mono text-gray-600 dark:text-gray-400">{account.swift}</p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingAccount(account)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        تعديل
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        حذف
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* General Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الإعدادات العامة للدفع</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="default_currency" className="arabic-text">العملة الافتراضية</Label>
                      <Input id="default_currency" value="MAD" readOnly />
                    </div>
                    <div>
                      <Label htmlFor="tax_rate" className="arabic-text">معدل الضريبة (%)</Label>
                      <Input id="tax_rate" type="number" defaultValue="20" />
                    </div>
                    <div>
                      <Label htmlFor="min_order_amount" className="arabic-text">الحد الأدنى للطلب (Dhs)</Label>
                      <Input id="min_order_amount" type="number" defaultValue="50" />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="max_cod_amount" className="arabic-text">الحد الأقصى للدفع عند الاستلام (Dhs)</Label>
                      <Input id="max_cod_amount" type="number" defaultValue="2000" />
                    </div>
                    <div>
                      <Label htmlFor="free_shipping_threshold" className="arabic-text">الحد الأدنى للشحن المجاني (Dhs)</Label>
                      <Input id="free_shipping_threshold" type="number" defaultValue="500" />
                    </div>
                    <div>
                      <Label htmlFor="payment_timeout" className="arabic-text">مهلة انتظار الدفع (دقائق)</Label>
                      <Input id="payment_timeout" type="number" defaultValue="30" />
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <Label htmlFor="payment_instructions" className="arabic-text">تعليمات الدفع للعملاء</Label>
                  <Textarea
                    id="payment_instructions"
                    placeholder="أدخل التعليمات التي ستظهر للعملاء أثناء عملية الدفع..."
                    className="mt-2 arabic-text"
                    rows={4}
                    defaultValue="يرجى التأكد من صحة المعلومات قبل إتمام عملية الدفع. في حالة وجود أي مشاكل، يرجى التواصل مع فريق الدعم."
                  />
                </div>

                <div className="flex justify-end">
                  <Button onClick={saveSettings} disabled={loading}>
                    {loading ? (
                      <>
                        <Settings className="h-4 w-4 mr-2 animate-spin" />
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        حفظ الإعدادات
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
