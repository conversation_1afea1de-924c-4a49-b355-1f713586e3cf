import { useState, useEffect } from 'react'
import { AIModel } from '@/types/ai-models'

interface UseAIModelsReturn {
  models: AIModel[]
  activeModels: AIModel[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useAIModels(): UseAIModelsReturn {
  const [models, setModels] = useState<AIModel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchModels = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/ai-models?include_inactive=true')
      
      if (!response.ok) {
        throw new Error('فشل في جلب نماذج الذكاء الاصطناعي')
      }
      
      const data = await response.json()
      setModels(data.models || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطأ غير معروف')
      console.error('Error fetching AI models:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchModels()
  }, [])

  // فلترة النماذج النشطة فقط
  const activeModels = models.filter(model => 
    model.isActive && 
    model.status === 'active' && 
    model.apiKey && 
    model.baseUrl
  )

  return {
    models,
    activeModels,
    loading,
    error,
    refetch: fetchModels
  }
}

// Hook مخصص للحصول على نموذج محدد للمهام النصية
export function useTextAIModel() {
  const { activeModels, loading, error } = useAIModels()
  
  // البحث عن أفضل نموذج نصي متاح
  const textModel = activeModels.find(model => 
    model.type === 'text' || 
    model.type === 'multimodal' ||
    model.provider === 'openai' ||
    model.provider === 'anthropic'
  )

  return {
    model: textModel,
    available: !!textModel,
    loading,
    error
  }
}

// Hook مخصص للحصول على نموذج محدد للصور
export function useImageAIModel() {
  const { activeModels, loading, error } = useAIModels()
  
  // البحث عن أفضل نموذج للصور متاح
  const imageModel = activeModels.find(model => 
    model.type === 'image' || 
    model.type === 'multimodal' ||
    model.provider === 'openai' ||
    model.provider === 'stability'
  )

  return {
    model: imageModel,
    available: !!imageModel,
    loading,
    error
  }
}
