"use client"

import { useState, useEffect } from 'react'

interface Stats {
  customers: number
  schools: number
  orders: number
  satisfaction: number
}

interface UseStatsReturn {
  stats: Stats
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useStats(): UseStatsReturn {
  const [stats, setStats] = useState<Stats>({
    customers: 1200,
    schools: 50,
    orders: 2500,
    satisfaction: 98
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data - replace with actual API response
      const mockStats: Stats = {
        customers: 1200 + Math.floor(Math.random() * 100),
        schools: 50 + Math.floor(Math.random() * 10),
        orders: 2500 + Math.floor(Math.random() * 200),
        satisfaction: 98
      }
      
      setStats(mockStats)
    } catch (err) {
      setError('فشل في تحميل الإحصائيات')
      console.error('Error fetching stats:', err)
    } finally {
      setLoading(false)
    }
  }

  const refetch = () => {
    fetchStats()
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return {
    stats,
    loading,
    error,
    refetch
  }
}
