import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'

// GET - جلب جميع المزودين
export async function GET() {
  try {
    // نظراً لأن localStorage لا يعمل في بيئة الخادم،
    // سنُرجع استجابة تخبر الكلاينت بجلب البيانات محلياً

    return NextResponse.json({
      success: true,
      providers: [], // سيتم جلبها من localStorage في الكلاينت
      message: 'تم جلب المزودين بنجاح',
      useLocalStorage: true // إشارة للكلاينت لاستخدام localStorage
    })
  } catch (error) {
    console.error('Error fetching AI providers:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في جلب المزودين',
        providers: []
      },
      { status: 500 }
    )
  }
}

// POST - إضافة مزود جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      provider,
      providerName,
      baseUrl,
      apiKey,
      models,
      description,
      status = 'active'
    } = body

    // التحقق من البيانات المطلوبة
    if (!provider || !providerName || !baseUrl || !apiKey || !models || models.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'جميع البيانات مطلوبة (المزود، الاسم، الرابط، مفتاح API، النماذج)' 
        },
        { status: 400 }
      )
    }

    // إنشاء مزود جديد
    const newProvider = {
      id: MockDataManager.generateId(),
      provider,
      providerName,
      baseUrl,
      apiKey,
      models,
      description: description || '',
      status,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // حفظ المزود
    const savedProvider = MockDataManager.addAIProvider(newProvider)

    return NextResponse.json({
      success: true,
      provider: savedProvider,
      message: `تم إضافة مزود ${providerName} بنجاح`
    })
  } catch (error) {
    console.error('Error adding AI provider:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في إضافة المزود' 
      },
      { status: 500 }
    )
  }
}

// PUT - تحديث مزود موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      provider,
      providerName,
      baseUrl,
      apiKey,
      models,
      description,
      status
    } = body

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'معرف المزود مطلوب' 
        },
        { status: 400 }
      )
    }

    // تحديث المزود
    const updatedProvider = {
      id,
      provider,
      providerName,
      baseUrl,
      apiKey,
      models,
      description: description || '',
      status,
      updatedAt: new Date().toISOString()
    }

    const result = MockDataManager.updateAIProvider(id, updatedProvider)

    if (!result) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'المزود غير موجود' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      provider: result,
      message: `تم تحديث مزود ${providerName} بنجاح`
    })
  } catch (error) {
    console.error('Error updating AI provider:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في تحديث المزود' 
      },
      { status: 500 }
    )
  }
}

// DELETE - حذف مزود
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'معرف المزود مطلوب' 
        },
        { status: 400 }
      )
    }

    const deleted = MockDataManager.deleteAIProvider(id)

    if (!deleted) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'المزود غير موجود' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المزود بنجاح'
    })
  } catch (error) {
    console.error('Error deleting AI provider:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في حذف المزود' 
      },
      { status: 500 }
    )
  }
}
