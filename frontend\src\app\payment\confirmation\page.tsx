"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { PageLayout } from '@/components/layouts/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  Eye,
  Home,
  Package,
  CreditCard,
  Building,
  Calendar,
  Hash,
  Mail,
  Phone,
  Truck,
  FileText
} from 'lucide-react'

interface PaymentConfirmation {
  order_id: string
  payment_method: 'bank_transfer' | 'cash_on_delivery' | 'credit_card'
  status: 'pending' | 'confirmed' | 'failed'
  amount: number
  currency: string
  transaction_id?: string
  confirmation_date: string
  estimated_processing_time?: string
  order_details: {
    items: Array<{
      name: string
      quantity: number
      price: number
      type: 'rental' | 'purchase'
    }>
    shipping_address: {
      name: string
      phone: string
      address: string
      city: string
    }
    shipping_cost: number
    tax_amount: number
  }
}

const paymentMethodLabels = {
  bank_transfer: 'التحويل البنكي',
  cash_on_delivery: 'الدفع عند الاستلام',
  credit_card: 'البطاقة الائتمانية'
}

const statusLabels = {
  pending: 'في انتظار المراجعة',
  confirmed: 'مؤكد',
  failed: 'فشل'
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  confirmed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const statusIcons = {
  pending: Clock,
  confirmed: CheckCircle,
  failed: AlertTriangle
}

export default function PaymentConfirmationPage() {
  const searchParams = useSearchParams()
  const [confirmation, setConfirmation] = useState<PaymentConfirmation | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const orderId = searchParams.get('order_id')
    const method = searchParams.get('method') as PaymentConfirmation['payment_method']
    
    // محاكاة جلب بيانات تأكيد الدفع
    setTimeout(() => {
      const mockConfirmation: PaymentConfirmation = {
        order_id: orderId || 'ORD-2024-001',
        payment_method: method || 'bank_transfer',
        status: method === 'bank_transfer' ? 'pending' : 'confirmed',
        amount: 396,
        currency: 'MAD',
        transaction_id: method === 'bank_transfer' ? undefined : 'TXN-' + Date.now(),
        confirmation_date: new Date().toISOString(),
        estimated_processing_time: method === 'bank_transfer' ? '2-4 ساعات عمل' : undefined,
        order_details: {
          items: [
            { name: 'ثوب التخرج الكلاسيكي', quantity: 1, price: 250, type: 'rental' },
            { name: 'قبعة التخرج', quantity: 1, price: 80, type: 'rental' }
          ],
          shipping_address: {
            name: 'أحمد محمد',
            phone: '+212-6XX-XXXXXX',
            address: 'شارع الحسن الثاني، رقم 123',
            city: 'بني ملال'
          },
          shipping_cost: 50,
          tax_amount: 66
        }
      }
      
      setConfirmation(mockConfirmation)
      setLoading(false)
    }, 1000)
  }, [searchParams])

  const downloadReceipt = () => {
    // في التطبيق الحقيقي، ستقوم بتوليد وتحميل الإيصال
    alert('سيتم تحميل الإيصال قريباً')
  }

  const trackOrder = () => {
    if (confirmation) {
      window.location.href = `/track-order?id=${confirmation.order_id}`
    }
  }

  if (loading) {
    return (
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <Clock className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            جاري تحميل تفاصيل الدفع...
          </h2>
        </div>
      </PageLayout>
    )
  }

  if (!confirmation) {
    return (
      <PageLayout containerClassName="container mx-auto px-4 py-8">
        <div className="text-center py-16">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            لم يتم العثور على تفاصيل الدفع
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6 arabic-text">
            يرجى التحقق من رقم الطلب أو التواصل مع الدعم
          </p>
          <Button asChild>
            <a href="/" className="arabic-text">
              العودة للصفحة الرئيسية
            </a>
          </Button>
        </div>
      </PageLayout>
    )
  }

  const StatusIcon = statusIcons[confirmation.status]

  return (
    <PageLayout containerClassName="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <StatusIcon className={`h-12 w-12 ${
              confirmation.status === 'confirmed' ? 'text-green-600' :
              confirmation.status === 'pending' ? 'text-yellow-600' : 'text-red-600'
            }`} />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
              {confirmation.status === 'confirmed' ? 'تم تأكيد الدفع' :
               confirmation.status === 'pending' ? 'الدفع في انتظار المراجعة' : 'فشل في الدفع'}
            </h1>
          </div>
          
          <Badge className={`text-lg px-4 py-2 ${statusColors[confirmation.status]}`}>
            {statusLabels[confirmation.status]}
          </Badge>
        </div>

        {/* Status Message */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            {confirmation.status === 'confirmed' && (
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
                <div>
                  <h3 className="text-xl font-bold text-green-900 dark:text-green-100 arabic-text">
                    تم تأكيد دفعتكم بنجاح!
                  </h3>
                  <p className="text-green-700 dark:text-green-300 mt-2 arabic-text">
                    شكراً لكم! تم استلام دفعتكم وسيتم معالجة طلبكم قريباً.
                  </p>
                </div>
              </div>
            )}

            {confirmation.status === 'pending' && (
              <div className="text-center space-y-4">
                <Clock className="h-16 w-16 text-yellow-600 mx-auto" />
                <div>
                  <h3 className="text-xl font-bold text-yellow-900 dark:text-yellow-100 arabic-text">
                    تم استلام إثبات الدفع
                  </h3>
                  <p className="text-yellow-700 dark:text-yellow-300 mt-2 arabic-text">
                    سيتم مراجعة إثبات الدفع خلال {confirmation.estimated_processing_time}. 
                    ستتلقون تأكيداً عبر البريد الإلكتروني عند الموافقة.
                  </p>
                </div>
              </div>
            )}

            {confirmation.status === 'failed' && (
              <div className="text-center space-y-4">
                <AlertTriangle className="h-16 w-16 text-red-600 mx-auto" />
                <div>
                  <h3 className="text-xl font-bold text-red-900 dark:text-red-100 arabic-text">
                    فشل في معالجة الدفع
                  </h3>
                  <p className="text-red-700 dark:text-red-300 mt-2 arabic-text">
                    حدث خطأ أثناء معالجة دفعتكم. يرجى المحاولة مرة أخرى أو التواصل مع الدعم.
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Payment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <CreditCard className="h-5 w-5" />
                تفاصيل الدفع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 arabic-text">رقم الطلب:</span>
                  <span className="font-mono">{confirmation.order_id}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 arabic-text">طريقة الدفع:</span>
                  <span className="arabic-text">{paymentMethodLabels[confirmation.payment_method]}</span>
                </div>

                {confirmation.transaction_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400 arabic-text">رقم المعاملة:</span>
                    <span className="font-mono">{confirmation.transaction_id}</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 arabic-text">المبلغ:</span>
                  <span className="font-bold text-lg">{confirmation.amount} {confirmation.currency}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 arabic-text">تاريخ التأكيد:</span>
                  <span>{new Date(confirmation.confirmation_date).toLocaleDateString('ar-MA')}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400 arabic-text">الحالة:</span>
                  <Badge className={statusColors[confirmation.status]}>
                    {statusLabels[confirmation.status]}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <Package className="h-5 w-5" />
                ملخص الطلب
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {confirmation.order_details.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium arabic-text">{item.name}</p>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <span>الكمية: {item.quantity}</span>
                        <Badge variant="outline" className="text-xs">
                          {item.type === 'rental' ? 'إيجار' : 'شراء'}
                        </Badge>
                      </div>
                    </div>
                    <span className="font-medium">{item.price} Dhs</span>
                  </div>
                ))}
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="arabic-text">المجموع الفرعي:</span>
                  <span>{confirmation.order_details.items.reduce((sum, item) => sum + item.price, 0)} Dhs</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">الشحن:</span>
                  <span>{confirmation.order_details.shipping_cost} Dhs</span>
                </div>
                <div className="flex justify-between">
                  <span className="arabic-text">الضريبة:</span>
                  <span>{confirmation.order_details.tax_amount} Dhs</span>
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center text-lg font-bold">
                <span className="arabic-text">المجموع الكلي:</span>
                <span className="text-blue-600">{confirmation.amount} Dhs</span>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <Truck className="h-5 w-5" />
                عنوان التوصيل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="font-medium arabic-text">{confirmation.order_details.shipping_address.name}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                <Phone className="h-4 w-4" />
                <span>{confirmation.order_details.shipping_address.phone}</span>
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                <p>{confirmation.order_details.shipping_address.address}</p>
                <p>{confirmation.order_details.shipping_address.city}</p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 arabic-text">
                <FileText className="h-5 w-5" />
                الإجراءات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button onClick={downloadReceipt} variant="outline" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                تحميل الإيصال
              </Button>
              
              <Button onClick={trackOrder} variant="outline" className="w-full">
                <Eye className="h-4 w-4 mr-2" />
                تتبع الطلب
              </Button>

              <Button asChild className="w-full">
                <a href="/">
                  <Home className="h-4 w-4 mr-2" />
                  العودة للصفحة الرئيسية
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Contact Support */}
        {confirmation.status === 'failed' && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="text-center arabic-text">تحتاج مساعدة؟</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-600 dark:text-gray-300 mb-6 arabic-text">
                فريق الدعم لدينا جاهز لمساعدتكم في حل أي مشاكل متعلقة بالدفع
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Phone className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium arabic-text">اتصل بنا</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">+212-5XX-XXXXXX</p>
                </div>
                <div className="text-center">
                  <div className="bg-green-100 dark:bg-green-900 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Mail className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-medium arabic-text">راسلنا</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </PageLayout>
  )
}
