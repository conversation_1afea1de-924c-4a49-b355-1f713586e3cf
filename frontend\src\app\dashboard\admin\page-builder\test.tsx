'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { UserRole } from '@/types/auth'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Sparkles } from 'lucide-react'
import Link from 'next/link'

export default function TestPageBuilder() {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        
        <main className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/dashboard/admin">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  العودة للوحة التحكم
                </Link>
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2">
                  <Sparkles className="h-8 w-8 text-purple-500" />
                  بناء الصفحات الذكية
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                  إنشاء صفحات احترافية باستخدام الذكاء الاصطناعي والقوالب الجاهزة
                </p>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">اختبار الصفحة</h2>
            <p>إذا كنت ترى هذا النص، فإن الصفحة تعمل بشكل صحيح!</p>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
