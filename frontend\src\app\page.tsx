"use client"

import { PageLayout } from "@/components/layouts/PageLayout"
import { HeroSection } from "@/components/home/<USER>"
import { StatsSection } from "@/components/home/<USER>"
import { FeaturesShowcase } from "@/components/home/<USER>"
import { ProductsPreview } from "@/components/home/<USER>"
import { TestimonialsSection } from "@/components/home/<USER>"
import { AIFeaturesSection } from "@/components/home/<USER>"
import { CTASection } from "@/components/home/<USER>"

export default function Home() {
  return (
    <PageLayout containerClassName="" showFooter={true}>
      {/* Hero Section - القسم الرئيسي */}
      <HeroSection />

      {/* Stats Section - قسم الإحصائيات */}
      <StatsSection />

      {/* Features Showcase - عرض الميزات */}
      <FeaturesShowcase />

      {/* Products Preview - معاينة المنتجات */}
      <ProductsPreview />

      {/* AI Features Section - قسم الذكاء الاصطناعي */}
      <AIFeaturesSection />

      {/* Testimonials Section - قسم آراء العملاء */}
      <TestimonialsSection />

      {/* Call to Action Section - قسم الدعوة للعمل */}
      <CTASection />
    </PageLayout>
  )
}
