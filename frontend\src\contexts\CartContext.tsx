"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// أنواع البيانات
export interface CartItem {
  id: string
  name: string
  price: number
  image: string
  quantity: number
  type: 'purchase' | 'rental'
  rental_price?: number
}

export interface WishlistItem {
  id: string
  name: string
  price: number
  image: string
  rental_price?: number
}

interface CartContextType {
  // السلة
  cartItems: CartItem[]
  cartCount: number
  addToCart: (productId: string, productData: any, type?: 'purchase' | 'rental') => void
  removeFromCart: (productId: string) => void
  updateQuantity: (productId: string, quantity: number) => void
  clearCart: () => void
  getCartTotal: () => number
  
  // المفضلة
  wishlistItems: WishlistItem[]
  wishlistCount: number
  addToWishlist: (productId: string, productData: any) => void
  removeFromWishlist: (productId: string) => void
  isInWishlist: (productId: string) => boolean
  clearWishlist: () => void
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([])

  // تحميل البيانات من localStorage عند بدء التشغيل
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem('cart')
      if (savedCart) {
        setCartItems(JSON.parse(savedCart))
      }

      const savedWishlist = localStorage.getItem('wishlist')
      if (savedWishlist) {
        setWishlistItems(JSON.parse(savedWishlist))
      }
    } catch (error) {
      console.error('Error loading cart/wishlist from localStorage:', error)
    }
  }, [])

  // حفظ السلة في localStorage عند التغيير
  useEffect(() => {
    try {
      localStorage.setItem('cart', JSON.stringify(cartItems))
      // تحديث localStorage القديم للتوافق مع Navigation
      localStorage.setItem('cartItems', JSON.stringify(cartItems))
    } catch (error) {
      console.error('Error saving cart to localStorage:', error)
    }
  }, [cartItems])

  // حفظ المفضلة في localStorage عند التغيير
  useEffect(() => {
    try {
      localStorage.setItem('wishlist', JSON.stringify(wishlistItems))
      // تحديث localStorage القديم للتوافق مع Navigation
      localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems))
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error)
    }
  }, [wishlistItems])

  // دوال السلة
  const addToCart = (productId: string, productData: any, type: 'purchase' | 'rental' = 'purchase') => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === productId && item.type === type)
      
      if (existingItem) {
        // زيادة الكمية إذا كان المنتج موجود
        return prev.map(item =>
          item.id === productId && item.type === type
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      } else {
        // إضافة منتج جديد
        const newItem: CartItem = {
          id: productId,
          name: productData.name,
          price: type === 'rental' ? (productData.rental_price || productData.price) : productData.price,
          image: productData.images?.[0] || productData.image || '/images/products/placeholder.jpg',
          quantity: 1,
          type,
          rental_price: productData.rental_price
        }
        return [...prev, newItem]
      }
    })
  }

  const removeFromCart = (productId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== productId))
  }

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId)
      return
    }

    setCartItems(prev =>
      prev.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      )
    )
  }

  const clearCart = () => {
    setCartItems([])
  }

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  // دوال المفضلة
  const addToWishlist = (productId: string, productData: any) => {
    setWishlistItems(prev => {
      const exists = prev.find(item => item.id === productId)
      if (exists) {
        return prev // المنتج موجود بالفعل
      }

      const newItem: WishlistItem = {
        id: productId,
        name: productData.name,
        price: productData.price,
        image: productData.images?.[0] || productData.image || '/images/products/placeholder.jpg',
        rental_price: productData.rental_price
      }
      return [...prev, newItem]
    })
  }

  const removeFromWishlist = (productId: string) => {
    setWishlistItems(prev => prev.filter(item => item.id !== productId))
  }

  const isInWishlist = (productId: string) => {
    return wishlistItems.some(item => item.id === productId)
  }

  const clearWishlist = () => {
    setWishlistItems([])
  }

  const value: CartContextType = {
    // السلة
    cartItems,
    cartCount: cartItems.reduce((total, item) => total + item.quantity, 0),
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    
    // المفضلة
    wishlistItems,
    wishlistCount: wishlistItems.length,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
