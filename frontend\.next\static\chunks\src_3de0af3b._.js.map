{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/admin/AdminStatsCards.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  Users,\n  School,\n  ShoppingCart,\n  DollarSign,\n  TrendingUp,\n  Truck\n} from 'lucide-react'\n\ninterface AdminStats {\n  total_users: number\n  total_schools: number\n  total_orders: number\n  total_revenue: number\n  monthly_growth: number\n  active_deliveries: number\n}\n\ninterface StatCard {\n  title: string\n  value: string | number\n  icon: React.ReactNode\n  color: string\n  href?: string\n  clickable: boolean\n  description?: string\n}\n\ninterface AdminStatsCardsProps {\n  stats: AdminStats\n}\n\nexport function AdminStatsCards({ stats }: AdminStatsCardsProps) {\n  const statCards: StatCard[] = [\n    {\n      title: 'إجمالي المستخدمين',\n      value: stats.total_users.toLocaleString(),\n      icon: <Users className=\"h-8 w-8 text-blue-600\" />,\n      color: 'blue',\n      href: '/dashboard/admin/users',\n      clickable: true,\n      description: 'انقر للإدارة'\n    },\n    {\n      title: 'المدارس المسجلة',\n      value: stats.total_schools,\n      icon: <School className=\"h-8 w-8 text-green-600\" />,\n      color: 'green',\n      clickable: false,\n      description: 'انقر للإدارة'\n    },\n    {\n      title: 'إجمالي الطلبات',\n      value: stats.total_orders.toLocaleString(),\n      icon: <ShoppingCart className=\"h-8 w-8 text-purple-600\" />,\n      color: 'purple',\n      href: '/dashboard/admin/products',\n      clickable: true,\n      description: 'انقر لإدارة المنتجات'\n    },\n    {\n      title: 'إجمالي الإيرادات',\n      value: `${(stats.total_revenue / 1000000).toFixed(1)}M Dhs`,\n      icon: <DollarSign className=\"h-8 w-8 text-yellow-600\" />,\n      color: 'yellow',\n      clickable: false\n    },\n    {\n      title: 'النمو الشهري',\n      value: `+${stats.monthly_growth}%`,\n      icon: <TrendingUp className=\"h-8 w-8 text-green-600\" />,\n      color: 'green',\n      clickable: false\n    },\n    {\n      title: 'عمليات التوصيل',\n      value: stats.active_deliveries,\n      icon: <Truck className=\"h-8 w-8 text-orange-600\" />,\n      color: 'orange',\n      clickable: false,\n      description: 'انقر للإدارة'\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\">\n      {statCards.map((card, index) => {\n        const CardComponent = (\n          <Card \n            key={index}\n            className={`${\n              card.clickable \n                ? 'hover:shadow-md transition-shadow cursor-pointer' \n                : card.description \n                  ? 'hover:shadow-md transition-shadow cursor-pointer opacity-75'\n                  : ''\n            }`}\n          >\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text\">\n                    {card.title}\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {card.value}\n                  </p>\n                  {card.description && (\n                    <p className={`text-xs mt-1 ${\n                      card.clickable ? 'text-blue-600' : 'text-gray-500'\n                    }`}>\n                      {card.description}\n                    </p>\n                  )}\n                </div>\n                {card.icon}\n              </div>\n            </CardContent>\n          </Card>\n        )\n\n        return card.clickable && card.href ? (\n          <Link key={index} href={card.href}>\n            {CardComponent}\n          </Link>\n        ) : (\n          CardComponent\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAoCO,SAAS,gBAAgB,EAAE,KAAK,EAAwB;IAC7D,MAAM,YAAwB;QAC5B;YACE,OAAO;YACP,OAAO,MAAM,WAAW,CAAC,cAAc;YACvC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,MAAM;YACN,WAAW;YACX,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,WAAW;YACX,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,YAAY,CAAC,cAAc;YACxC,oBAAM,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;YACP,MAAM;YACN,WAAW;YACX,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,GAAG,CAAC,MAAM,aAAa,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;YAC3D,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,WAAW;QACb;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,CAAC,CAAC;YAClC,oBAAM,6LAAC,qNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,WAAW;QACb;QACA;YACE,OAAO;YACP,OAAO,MAAM,iBAAiB;YAC9B,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,WAAW;YACX,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM;YACpB,MAAM,8BACJ,6LAAC,mIAAA,CAAA,OAAI;gBAEH,WAAW,GACT,KAAK,SAAS,GACV,qDACA,KAAK,WAAW,GACd,gEACA,IACN;0BAEF,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAE,WAAU;kDACV,KAAK,KAAK;;;;;;oCAEZ,KAAK,WAAW,kBACf,6LAAC;wCAAE,WAAW,CAAC,aAAa,EAC1B,KAAK,SAAS,GAAG,kBAAkB,iBACnC;kDACC,KAAK,WAAW;;;;;;;;;;;;4BAItB,KAAK,IAAI;;;;;;;;;;;;eA1BT;;;;;YAgCT,OAAO,KAAK,SAAS,IAAI,KAAK,IAAI,iBAChC,6LAAC,+JAAA,CAAA,UAAI;gBAAa,MAAM,KAAK,IAAI;0BAC9B;eADQ;;;;uBAIX;QAEJ;;;;;;AAGN;KAnGgB", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/dashboard/admin/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Navigation } from '@/components/Navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Input } from '@/components/ui/input'\nimport Link from 'next/link'\nimport { QuickAdminActions } from '@/components/admin/QuickAdminActions'\nimport { AdminStatsCards } from '@/components/admin/AdminStatsCards'\nimport { AdminQuickNav } from '@/components/admin/AdminQuickNav'\nimport { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'\nimport {\n  Shield,\n  Users,\n  ShoppingCart,\n  TrendingUp,\n  Calendar,\n  FileText,\n  Download,\n  Upload,\n  Plus,\n  Eye,\n  Edit,\n  Search,\n  Filter,\n  BarChart3,\n  PieChart,\n  DollarSign,\n  Package,\n  Settings,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  School,\n  Truck,\n  Menu,\n  ArrowRight,\n  Building,\n  User,\n  Folder,\n  Brain,\n  Wand2,\n  Layout,\n  Palette,\n  Crown,\n  Badge as BadgeIcon,\n  Activity,\n  Star,\n  Heart,\n  MessageSquare,\n  CreditCard\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface AdminStats {\n  total_users: number\n  total_schools: number\n  total_orders: number\n  total_revenue: number\n  monthly_growth: number\n  active_deliveries: number\n}\n\ninterface SystemAlert {\n  id: string\n  type: 'error' | 'warning' | 'info' | 'success'\n  title: string\n  message: string\n  timestamp: string\n  resolved: boolean\n}\n\ninterface RecentActivity {\n  id: string\n  type: 'order' | 'user' | 'school' | 'system'\n  description: string\n  timestamp: string\n  user?: string\n}\n\nexport default function AdminDashboard() {\n  const { user, profile, loading: authLoading } = useAuth()\n  const [activeTab, setActiveTab] = useState('overview')\n  const [stats, setStats] = useState<AdminStats>({\n    total_users: 0,\n    total_schools: 0,\n    total_orders: 0,\n    total_revenue: 0,\n    monthly_growth: 0,\n    active_deliveries: 0\n  })\n  const [alerts, setAlerts] = useState<SystemAlert[]>([])\n  const [activities, setActivities] = useState<RecentActivity[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // بيانات وهمية للتطوير\n  useEffect(() => {\n    const mockStats: AdminStats = {\n      total_users: 1247,\n      total_schools: 45,\n      total_orders: 3892,\n      total_revenue: 1234567.89,\n      monthly_growth: 12.5,\n      active_deliveries: 156\n    }\n\n    const mockAlerts: SystemAlert[] = [\n      {\n        id: '1',\n        type: 'warning',\n        title: 'مخزون منخفض',\n        message: 'مخزون أزياء التخرج الكلاسيكية أقل من 50 قطعة',\n        timestamp: '2024-01-20T10:30:00Z',\n        resolved: false\n      },\n      {\n        id: '2',\n        type: 'info',\n        title: 'طلب جماعي جديد',\n        message: 'مدرسة الأمل قدمت طلب جماعي لـ 120 طالب',\n        timestamp: '2024-01-20T09:15:00Z',\n        resolved: false\n      },\n      {\n        id: '3',\n        type: 'success',\n        title: 'تحديث النظام',\n        message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',\n        timestamp: '2024-01-19T22:00:00Z',\n        resolved: true\n      }\n    ]\n\n    const mockActivities: RecentActivity[] = [\n      {\n        id: '1',\n        type: 'order',\n        description: 'طلب جديد من أحمد محمد - زي التخرج الكلاسيكي',\n        timestamp: '2024-01-20T11:00:00Z',\n        user: 'أحمد محمد'\n      },\n      {\n        id: '2',\n        type: 'school',\n        description: 'تسجيل مدرسة جديدة - مدرسة النور الثانوية',\n        timestamp: '2024-01-20T10:45:00Z'\n      },\n      {\n        id: '3',\n        type: 'user',\n        description: 'مستخدم جديد انضم للمنصة - فاطمة أحمد',\n        timestamp: '2024-01-20T10:30:00Z',\n        user: 'فاطمة أحمد'\n      },\n      {\n        id: '4',\n        type: 'system',\n        description: 'تم إنشاء نسخة احتياطية من قاعدة البيانات',\n        timestamp: '2024-01-20T02:00:00Z'\n      }\n    ]\n\n    setStats(mockStats)\n    setAlerts(mockAlerts)\n    setActivities(mockActivities)\n    setLoading(false)\n  }, [])\n\n  const getAlertIcon = (type: SystemAlert['type']) => {\n    switch (type) {\n      case 'error': return <AlertTriangle className=\"h-5 w-5 text-red-600\" />\n      case 'warning': return <AlertTriangle className=\"h-5 w-5 text-yellow-600\" />\n      case 'info': return <Clock className=\"h-5 w-5 text-blue-600\" />\n      case 'success': return <CheckCircle className=\"h-5 w-5 text-green-600\" />\n      default: return <Clock className=\"h-5 w-5 text-gray-600\" />\n    }\n  }\n\n  const getAlertColor = (type: SystemAlert['type']) => {\n    switch (type) {\n      case 'error': return 'bg-red-50 border-red-200 dark:bg-red-900/20'\n      case 'warning': return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20'\n      case 'info': return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20'\n      case 'success': return 'bg-green-50 border-green-200 dark:bg-green-900/20'\n      default: return 'bg-gray-50 border-gray-200 dark:bg-gray-900/20'\n    }\n  }\n\n  const getActivityIcon = (type: RecentActivity['type']) => {\n    switch (type) {\n      case 'order': return <ShoppingCart className=\"h-4 w-4 text-blue-600\" />\n      case 'user': return <Users className=\"h-4 w-4 text-green-600\" />\n      case 'school': return <School className=\"h-4 w-4 text-purple-600\" />\n      case 'system': return <Settings className=\"h-4 w-4 text-gray-600\" />\n      default: return <Clock className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n        <Navigation />\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600\"></div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2\">\n                <Crown className=\"h-8 w-8 text-yellow-500\" />\n                لوحة تحكم الإدارة\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-300 mt-2 arabic-text\">\n                إدارة شاملة للمنصة والمستخدمين والطلبات\n              </p>\n            </div>\n\n            {/* Navigation Links */}\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\" className=\"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-2 text-sm transition-colors\">\n                <span>الصفحة الرئيسية</span>\n                <ArrowRight className=\"h-4 w-4\" />\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <AdminStatsCards stats={stats} />\n\n        {/* Main Content Tabs */}\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-5\">\n            <TabsTrigger value=\"overview\" className=\"arabic-text\">\n              <BarChart3 className=\"h-4 w-4 mr-2\" />\n              نظرة عامة\n            </TabsTrigger>\n            <TabsTrigger value=\"users\" className=\"arabic-text\">\n              <Users className=\"h-4 w-4 mr-2\" />\n              المستخدمين\n            </TabsTrigger>\n            <TabsTrigger value=\"orders\" className=\"arabic-text\">\n              <Package className=\"h-4 w-4 mr-2\" />\n              الطلبات\n            </TabsTrigger>\n            <TabsTrigger value=\"analytics\" className=\"arabic-text\">\n              <PieChart className=\"h-4 w-4 mr-2\" />\n              التحليلات\n            </TabsTrigger>\n            <TabsTrigger value=\"settings\" className=\"arabic-text\">\n              <Settings className=\"h-4 w-4 mr-2\" />\n              الإعدادات\n            </TabsTrigger>\n          </TabsList>\n\n          {/* Overview Tab */}\n          <TabsContent value=\"overview\" className=\"space-y-6 mt-6\">\n            {/* Quick Actions for Overview */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text flex items-center gap-2\">\n                  <Plus className=\"h-5 w-5\" />\n                  الإجراءات السريعة\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  <Link href=\"/dashboard/admin/pages-management\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <FileText className=\"h-4 w-4 mr-2\" />\n                      إضافة صفحة جديدة\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/menu-management\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Menu className=\"h-4 w-4 mr-2\" />\n                      تحرير القائمة\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/products\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Package className=\"h-4 w-4 mr-2\" />\n                      إضافة منتج\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/orders\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      إدارة الطلبات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/categories\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Folder className=\"h-4 w-4 mr-2\" />\n                      إدارة الفئات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/users\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      إدارة المستخدمين\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/ai-models\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Brain className=\"h-4 w-4 mr-2\" />\n                      نماذج الذكاء الاصطناعي\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/page-builder\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Wand2 className=\"h-4 w-4 mr-2\" />\n                      بناء الصفحات الذكية\n                    </Button>\n                  </Link>\n                </div>\n              </CardContent>\n            </Card>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* Revenue Chart */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">الإيرادات الشهرية</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <div className=\"text-center\">\n                      <BarChart3 className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                      <p className=\"text-gray-500 arabic-text\">رسم بياني للإيرادات</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* User Growth Chart */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">نمو المستخدمين</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <div className=\"text-center\">\n                      <TrendingUp className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                      <p className=\"text-gray-500 arabic-text\">رسم بياني للنمو</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Recent Activity */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">النشاط الأخير</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {activities.slice(0, 8).map((activity) => (\n                    <div key={activity.id} className=\"flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                      <div className=\"w-8 h-8 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center\">\n                        {getActivityIcon(activity.type)}\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm arabic-text\">{activity.description}</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {new Date(activity.timestamp).toLocaleString('en-US')}\n                        </p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Users Tab */}\n          <TabsContent value=\"users\" className=\"space-y-6 mt-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              {/* User Stats */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">إحصائيات المستخدمين</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي المستخدمين</span>\n                      <span className=\"font-bold\">{stats.total_users.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">المستخدمين النشطين</span>\n                      <span className=\"font-bold text-green-600\">892</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">مستخدمين جدد هذا الشهر</span>\n                      <span className=\"font-bold text-blue-600\">156</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Quick User Actions */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">إجراءات سريعة</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <Link href=\"/dashboard/admin/users\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      إدارة المستخدمين\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/schools\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <School className=\"h-4 w-4 mr-2\" />\n                      إدارة المدارس\n                    </Button>\n                  </Link>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    إضافة مستخدم جديد\n                  </Button>\n                </CardContent>\n              </Card>\n\n              {/* User Types */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">أنواع المستخدمين</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">طلاب</span>\n                      <Badge variant=\"secondary\">856</Badge>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">مدارس</span>\n                      <Badge variant=\"secondary\">{stats.total_schools}</Badge>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">إداريين</span>\n                      <Badge variant=\"secondary\">12</Badge>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">شركاء توصيل</span>\n                      <Badge variant=\"secondary\">8</Badge>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Recent Users */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">المستخدمين الجدد</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {[\n                    { name: 'أحمد محمد', email: '<EMAIL>', type: 'طالب', date: '2024-01-20' },\n                    { name: 'فاطمة أحمد', email: '<EMAIL>', type: 'طالبة', date: '2024-01-19' },\n                    { name: 'مدرسة النور', email: '<EMAIL>', type: 'مدرسة', date: '2024-01-18' },\n                    { name: 'يوسف علي', email: '<EMAIL>', type: 'طالب', date: '2024-01-17' }\n                  ].map((user, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                          <User className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n                        </div>\n                        <div>\n                          <p className=\"font-medium arabic-text\">{user.name}</p>\n                          <p className=\"text-sm text-gray-500\">{user.email}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <Badge variant=\"outline\">{user.type}</Badge>\n                        <p className=\"text-xs text-gray-500 mt-1\">{user.date}</p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Orders Tab */}\n          <TabsContent value=\"orders\" className=\"space-y-6 mt-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n              {/* Order Stats */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">الطلبات</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-blue-600\">{stats.total_orders.toLocaleString()}</div>\n                    <p className=\"text-sm text-gray-500\">إجمالي الطلبات</p>\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">الإيرادات</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-green-600\">{(stats.total_revenue / 1000).toFixed(0)}K</div>\n                    <p className=\"text-sm text-gray-500\">درهم مغربي</p>\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">قيد التوصيل</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-orange-600\">{stats.active_deliveries}</div>\n                    <p className=\"text-sm text-gray-500\">طلب نشط</p>\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">النمو الشهري</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple-600\">+{stats.monthly_growth}%</div>\n                    <p className=\"text-sm text-gray-500\">مقارنة بالشهر الماضي</p>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Quick Order Actions */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">إدارة الطلبات</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <Link href=\"/dashboard/admin/orders\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      عرض جميع الطلبات\n                    </Button>\n                  </Link>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <Clock className=\"h-4 w-4 mr-2\" />\n                    الطلبات المعلقة\n                  </Button>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <CheckCircle className=\"h-4 w-4 mr-2\" />\n                    الطلبات المكتملة\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Recent Orders */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"arabic-text\">الطلبات الأخيرة</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {[\n                    { id: '#1234', customer: 'أحمد محمد', product: 'زي التخرج الكلاسيكي', amount: '450 Dhs', status: 'قيد التحضير' },\n                    { id: '#1235', customer: 'فاطمة أحمد', product: 'طقم التخرج الفاخر', amount: '680 Dhs', status: 'تم الشحن' },\n                    { id: '#1236', customer: 'مدرسة النور', product: 'طلب جماعي - 120 قطعة', amount: '54,000 Dhs', status: 'قيد المراجعة' },\n                    { id: '#1237', customer: 'يوسف علي', product: 'زي التخرج المميز', amount: '520 Dhs', status: 'مكتمل' }\n                  ].map((order, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\">\n                          <ShoppingCart className=\"h-5 w-5 text-green-600 dark:text-green-400\" />\n                        </div>\n                        <div>\n                          <p className=\"font-medium arabic-text\">{order.id} - {order.customer}</p>\n                          <p className=\"text-sm text-gray-500\">{order.product}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"font-bold\">{order.amount}</p>\n                        <Badge variant={order.status === 'مكتمل' ? 'default' : 'secondary'}>\n                          {order.status}\n                        </Badge>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Analytics Tab */}\n          <TabsContent value=\"analytics\" className=\"space-y-6 mt-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* Revenue Analytics */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">تحليل الإيرادات</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <div className=\"text-center\">\n                      <DollarSign className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                      <p className=\"text-gray-500 arabic-text\">رسم بياني للإيرادات</p>\n                      <p className=\"text-sm text-gray-400 mt-2\">سيتم إضافة الرسوم البيانية قريباً</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* User Analytics */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">تحليل المستخدمين</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                    <div className=\"text-center\">\n                      <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                      <p className=\"text-gray-500 arabic-text\">إحصائيات المستخدمين</p>\n                      <p className=\"text-sm text-gray-400 mt-2\">تحليل نمو وسلوك المستخدمين</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Product Analytics */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">تحليل المنتجات</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">الأكثر مبيعاً</span>\n                      <span className=\"font-medium\">زي التخرج الكلاسيكي</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">الأعلى تقييماً</span>\n                      <span className=\"font-medium\">طقم التخرج الفاخر</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">الأكثر طلباً</span>\n                      <span className=\"font-medium\">زي التخرج المميز</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Performance Metrics */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">مؤشرات الأداء</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-1\">\n                        <span>معدل التحويل</span>\n                        <span>3.2%</span>\n                      </div>\n                      <Progress value={32} className=\"h-2\" />\n                    </div>\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-1\">\n                        <span>رضا العملاء</span>\n                        <span>94%</span>\n                      </div>\n                      <Progress value={94} className=\"h-2\" />\n                    </div>\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-1\">\n                        <span>معدل الإرجاع</span>\n                        <span>2.1%</span>\n                      </div>\n                      <Progress value={21} className=\"h-2\" />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n\n          {/* Settings Tab */}\n          <TabsContent value=\"settings\" className=\"space-y-6 mt-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* Content Management */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">إدارة المحتوى</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Link href=\"/dashboard/admin/pages-management\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <FileText className=\"h-4 w-4 mr-2\" />\n                      إدارة الصفحات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/menu-management\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Menu className=\"h-4 w-4 mr-2\" />\n                      إدارة القائمة الرئيسية\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/users\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      إدارة المستخدمين\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/products\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Package className=\"h-4 w-4 mr-2\" />\n                      إدارة المنتجات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/orders\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      إدارة الطلبات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/categories\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Folder className=\"h-4 w-4 mr-2\" />\n                      إدارة الفئات\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/page-builder\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Wand2 className=\"h-4 w-4 mr-2\" />\n                      بناء الصفحات الذكية\n                    </Button>\n                  </Link>\n                </CardContent>\n              </Card>\n\n              {/* AI & Advanced Features */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">الذكاء الاصطناعي والميزات المتقدمة</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Link href=\"/dashboard/admin/ai-models\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Brain className=\"h-4 w-4 mr-2\" />\n                      إدارة نماذج الذكاء الاصطناعي\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/page-builder\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Layout className=\"h-4 w-4 mr-2\" />\n                      بناء الصفحات الذكية\n                    </Button>\n                  </Link>\n                  <Button className=\"w-full justify-start\" variant=\"outline\" disabled>\n                    <Palette className=\"h-4 w-4 mr-2\" />\n                    مولد المحتوى (قريباً)\n                  </Button>\n                </CardContent>\n              </Card>\n\n              {/* User Management */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">إدارة المستخدمين</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Link href=\"/dashboard/admin/users\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      إدارة المستخدمين\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/schools\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <School className=\"h-4 w-4 mr-2\" />\n                      إدارة المدارس\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/delivery\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Truck className=\"h-4 w-4 mr-2\" />\n                      إدارة التوصيل\n                    </Button>\n                  </Link>\n                </CardContent>\n              </Card>\n\n              {/* System Settings */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"arabic-text\">إعدادات النظام</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Link href=\"/dashboard/admin/settings\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      الإعدادات العامة\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/checkout-settings\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      إعدادات صفحة الدفع\n                    </Button>\n                  </Link>\n                  <Link href=\"/dashboard/admin/payment-methods\">\n                    <Button className=\"w-full justify-start\" variant=\"outline\">\n                      <CreditCard className=\"h-4 w-4 mr-2\" />\n                      إدارة طرق الدفع\n                    </Button>\n                  </Link>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <Shield className=\"h-4 w-4 mr-2\" />\n                    إعدادات الأمان\n                  </Button>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <Download className=\"h-4 w-4 mr-2\" />\n                    تصدير البيانات\n                  </Button>\n                  <Button className=\"w-full justify-start\" variant=\"outline\">\n                    <Upload className=\"h-4 w-4 mr-2\" />\n                    استيراد البيانات\n                  </Button>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n        </Tabs>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhBA;;;;;;;;;;;;AAqFe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAC7C,aAAa;QACb,eAAe;QACf,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,mBAAmB;IACrB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,YAAwB;gBAC5B,aAAa;gBACb,eAAe;gBACf,cAAc;gBACd,eAAe;gBACf,gBAAgB;gBAChB,mBAAmB;YACrB;YAEA,MAAM,aAA4B;gBAChC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW;oBACX,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,WAAW;oBACX,UAAU;gBACZ;aACD;YAED,MAAM,iBAAmC;gBACvC;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;oBACX,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,WAAW;gBACb;aACD;YAED,SAAS;YACT,UAAU;YACV,cAAc;YACd,WAAW;QACb;mCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAW,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACrC,KAAK;gBAAW,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAQ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACrC,KAAK;gBAAU,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAU,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC1C;gBAAS,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;8BACX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,aAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;sDAG/C,6LAAC;4CAAE,WAAU;sDAAoD;;;;;;;;;;;;8CAMnE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;0DAAK;;;;;;0DACN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,6LAAC,iJAAA,CAAA,kBAAe;wBAAC,OAAO;;;;;;kCAGxB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;;0CACrC,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;;0DACnC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,WAAU;;0DACpC,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGtC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMzC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDAEtC,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIhC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIzC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIrC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,2MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIxC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAI7C,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIvC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAItC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAItC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,kNAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ5C,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOjD,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQnD,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc;;;;;;;;;;;0DAErC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC3B,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEAAI,WAAU;8EACZ,gBAAgB,SAAS,IAAI;;;;;;8EAEhC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAuB,SAAS,WAAW;;;;;;sFACxD,6LAAC;4EAAE,WAAU;sFACV,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;2DAPzC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAkB/B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;;kDACnC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA2C;;;;;;sFAC3D,6LAAC;4EAAK,WAAU;sFAAa,MAAM,WAAW,CAAC,cAAc;;;;;;;;;;;;8EAE/D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA2C;;;;;;sFAC3D,6LAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;;8EAE7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA2C;;;;;;sFAC3D,6LAAC;4EAAK,WAAU;sFAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOlD,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;oEAAuB,SAAQ;;sFAC/C,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;0EAItC,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;0EACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;oEAAuB,SAAQ;;sFAC/C,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;0EAIvC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;0DAOvC,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAY;;;;;;;;;;;;8EAE7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAa,MAAM,aAAa;;;;;;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAY;;;;;;;;;;;;8EAE7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQrC,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc;;;;;;;;;;;0DAErC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,MAAM;4DAAa,OAAO;4DAAqB,MAAM;4DAAQ,MAAM;wDAAa;wDAClF;4DAAE,MAAM;4DAAc,OAAO;4DAAsB,MAAM;4DAAS,MAAM;wDAAa;wDACrF;4DAAE,MAAM;4DAAe,OAAO;4DAAsB,MAAM;4DAAS,MAAM;wDAAa;wDACtF;4DAAE,MAAM;4DAAY,OAAO;4DAAuB,MAAM;4DAAQ,MAAM;wDAAa;qDACpF,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,6LAAC;;8FACC,6LAAC;oFAAE,WAAU;8FAA2B,KAAK,IAAI;;;;;;8FACjD,6LAAC;oFAAE,WAAU;8FAAyB,KAAK,KAAK;;;;;;;;;;;;;;;;;;8EAGpD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAW,KAAK,IAAI;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFAA8B,KAAK,IAAI;;;;;;;;;;;;;2DAZ9C;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAsBpB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;;kDACpC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAoC,MAAM,YAAY,CAAC,cAAc;;;;;;8EACpF,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAqC,CAAC,MAAM,aAAa,GAAG,IAAI,EAAE,OAAO,CAAC;wEAAG;;;;;;;8EAC5F,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAsC,MAAM,iBAAiB;;;;;;8EAC5E,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;kEAErC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAqC;wEAAE,MAAM,cAAc;wEAAC;;;;;;;8EAC3E,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc;;;;;;;;;;;0DAErC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAI7C,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGpC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAQhD,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc;;;;;;;;;;;0DAErC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,IAAI;4DAAS,UAAU;4DAAa,SAAS;4DAAuB,QAAQ;4DAAW,QAAQ;wDAAc;wDAC/G;4DAAE,IAAI;4DAAS,UAAU;4DAAc,SAAS;4DAAqB,QAAQ;4DAAW,QAAQ;wDAAW;wDAC3G;4DAAE,IAAI;4DAAS,UAAU;4DAAe,SAAS;4DAAwB,QAAQ;4DAAc,QAAQ;wDAAe;wDACtH;4DAAE,IAAI;4DAAS,UAAU;4DAAY,SAAS;4DAAoB,QAAQ;4DAAW,QAAQ;wDAAQ;qDACtG,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;sFAE1B,6LAAC;;8FACC,6LAAC;oFAAE,WAAU;;wFAA2B,MAAM,EAAE;wFAAC;wFAAI,MAAM,QAAQ;;;;;;;8FACnE,6LAAC;oFAAE,WAAU;8FAAyB,MAAM,OAAO;;;;;;;;;;;;;;;;;;8EAGvD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAa,MAAM,MAAM;;;;;;sFACtC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAS,MAAM,MAAM,KAAK,UAAU,YAAY;sFACpD,MAAM,MAAM;;;;;;;;;;;;;2DAbT;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAwBpB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlD,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOlD,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOtC,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC,uIAAA,CAAA,WAAQ;wEAAC,OAAO;wEAAI,WAAU;;;;;;;;;;;;0EAEjC,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC,uIAAA,CAAA,WAAQ;wEAAC,OAAO;wEAAI,WAAU;;;;;;;;;;;;0EAEjC,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC,uIAAA,CAAA,WAAQ;wEAAC,OAAO;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIzC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIrC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAItC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,2MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIxC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAI7C,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIvC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,kNAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAQ1C,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAItC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,wNAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIvC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;4DAAU,QAAQ;;8EACjE,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO1C,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAItC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIvC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAQ1C,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAc;;;;;;;;;;;8DAErC,6LAAC,mIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIzC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAI7C,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,SAAQ;;kFAC/C,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAI3C,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGrC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAuB,SAAQ;;8EAC/C,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;GAzwBwB;;QAC0B,kIAAA,CAAA,UAAO;;;KADjC", "debugId": null}}]}