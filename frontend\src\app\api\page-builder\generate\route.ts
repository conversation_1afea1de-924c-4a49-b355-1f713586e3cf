import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager } from '@/lib/mockData'
import { AIGenerationRequest, AIGenerationResponse, PageComponent } from '@/types/page-builder'

// POST - توليد صفحة بالذكاء الاصطناعي
export async function POST(request: NextRequest) {
  try {
    const body: AIGenerationRequest = await request.json()
    const {
      prompt,
      language,
      category,
      style,
      colors,
      includeImages,
      includeText,
      pageType,
      targetAudience,
      businessType,
      modelId,
      includeMainHeader,
      mainMenuItems
    } = body

    // التحقق من البيانات المطلوبة
    if (!prompt || !language) {
      return NextResponse.json(
        { error: 'الوصف واللغة مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من النموذج المحدد
    let selectedProvider = null
    let selectedModelName = null

    if (modelId) {
      // تحليل modelId (format: providerId-modelName)
      const modelParts = modelId.split('-')
      const providerId = modelParts[0]
      const modelName = modelParts.slice(1).join('-') // في حالة وجود أكثر من dash في اسم النموذج

      // محاكاة البحث عن المزود (نظراً لأن localStorage لا يعمل في الخادم)
      // سنقبل أي modelId ونعتبره صالحاً للمحاكاة
      selectedProvider = {
        id: providerId,
        providerName: 'Google AI', // افتراضي للمحاكاة
        status: 'active',
        models: [modelName]
      }
      selectedModelName = modelName
    } else {
      return NextResponse.json(
        { error: 'لم يتم تحديد نموذج للتوليد' },
        { status: 400 }
      )
    }

    if (!selectedProvider || !selectedModelName) {
      return NextResponse.json(
        { error: 'لا يوجد نموذج متاح للتوليد. يرجى إضافة وتفعيل مزود ذكاء اصطناعي أولاً.' },
        { status: 400 }
      )
    }

    // محاكاة وقت التوليد
    const startTime = Date.now()
    const generationTime = Math.floor(Math.random() * 5000) + 2000 // 2-7 ثواني

    // محاكاة نجاح التوليد بناءً على نوع المزود
    const providerSuccessRates = {
      'OpenAI': 0.95,
      'Anthropic': 0.92,
      'Google AI': 0.90,
      'Microsoft Azure OpenAI': 0.88,
      'Grok (xAI)': 0.85,
      'DeepSeek': 0.85
    }

    const successRate = providerSuccessRates[selectedProvider.providerName as keyof typeof providerSuccessRates] || 0.90
    const success = true // دائماً ناجح للاختبار

    if (!success) {
      return NextResponse.json({
        success: false,
        error: `فشل في توليد الصفحة باستخدام ${selectedProvider.providerName}. يرجى المحاولة مرة أخرى.`,
        metadata: {
          tokensUsed: 0,
          generationTime: Date.now() - startTime,
          modelUsed: `${selectedProvider.providerName} - ${selectedModelName}`
        }
      } as AIGenerationResponse)
    }

    // توليد مكونات الصفحة بناءً على الوصف
    let components: PageComponent[]
    try {
      components = generatePageComponents(prompt, {
        language,
        category,
        style,
        colors,
        includeImages,
        includeText,
        pageType,
        targetAudience,
        businessType,
        includeMainHeader,
        mainMenuItems
      })
    } catch (error) {
      console.error('Error in generatePageComponents:', error)
      return NextResponse.json(
        { error: 'خطأ في توليد مكونات الصفحة' },
        { status: 500 }
      )
    }

    // حساب الرموز المستخدمة (تقدير)
    const tokensUsed = Math.ceil(prompt.length / 4) + Math.floor(Math.random() * 1000) + 500

    // حساب التكلفة
    const cost = (tokensUsed / 1000) * 0.002

    // تحديث إحصائيات النموذج (محاكاة بسيطة)

    // إضافة نشاط
    const activities = MockDataManager.getModelActivities()
    activities.push({
      id: MockDataManager.generateId(),
      modelId: `${selectedProvider.id}-${selectedModelName}`,
      type: 'request',
      description: `توليد صفحة: ${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}`,
      details: {
        prompt,
        language,
        category,
        pageType,
        componentsGenerated: components.length,
        provider: selectedProvider.providerName,
        model: selectedModelName
      },
      timestamp: new Date().toISOString(),
      duration: generationTime,
      tokensUsed,
      cost,
      success: true
    })
    MockDataManager.saveModelActivities(activities)

    // إنشاء اقتراحات للتحسين
    const suggestions = generateSuggestions(prompt, components)

    const response: AIGenerationResponse = {
      success: true,
      components,
      suggestions,
      metadata: {
        tokensUsed,
        generationTime,
        modelUsed: `${selectedProvider.providerName} - ${selectedModelName}`,
        componentsCount: components.length,
        estimatedCost: cost
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error generating page:', error)
    console.error('Error details:', error instanceof Error ? error.message : 'Unknown error')
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { error: 'خطأ في توليد الصفحة: ' + (error instanceof Error ? error.message : 'خطأ غير معروف') },
      { status: 500 }
    )
  }
}

// دالة مساعدة لتوليد مكونات الصفحة
function generatePageComponents(prompt: string, options: any): PageComponent[] {
  const components: PageComponent[] = []

  // إضافة Hero Section بسيط
  components.push({
    id: MockDataManager.generateId(),
    type: 'hero',
    name: 'Hero Section',
    props: {
      content: 'مرحباً بكم في موقعنا',
      style: {
        backgroundColor: '#1F2937',
        color: '#FFFFFF',
        textAlign: 'center',
        padding: '4rem 2rem'
      }
    },
    position: { x: 0, y: 0 },
    size: { width: '100%', height: '500px' },
    isVisible: true
  })

  return components
}

// دالة مساعدة لاستخراج العنوان
function extractTitle(prompt: string): string | null {
  const titlePatterns = [
    /عنوان[:\s]+([^.،]+)/,
    /اسم[:\s]+([^.،]+)/,
    /موقع[:\s]+([^.،]+)/,
    /صفحة[:\s]+([^.،]+)/
  ]

  for (const pattern of titlePatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لاستخراج العنوان الفرعي
function extractSubtitle(prompt: string): string | null {
  const subtitlePatterns = [
    /وصف[:\s]+([^.،]+)/,
    /شعار[:\s]+([^.،]+)/,
    /نبذة[:\s]+([^.،]+)/
  ]

  for (const pattern of subtitlePatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لاستخراج محتوى "عن الشركة"
function extractAboutContent(prompt: string): string | null {
  const aboutPatterns = [
    /عن الشركة[:\s]+([^.]+)/,
    /حول[:\s]+([^.]+)/,
    /نحن[:\s]+([^.]+)/
  ]

  for (const pattern of aboutPatterns) {
    const match = prompt.match(pattern)
    if (match) {
      return match[1].trim()
    }
  }

  return null
}

// دالة مساعدة لتوليد اقتراحات التحسين
function generateSuggestions(prompt: string, components: PageComponent[]): string[] {
  const suggestions = []

  if (components.length < 3) {
    suggestions.push('يمكنك إضافة المزيد من الأقسام لجعل الصفحة أكثر تفصيلاً')
  }

  if (!components.some(c => c.type === 'contact')) {
    suggestions.push('فكر في إضافة نموذج اتصال لتسهيل التواصل مع الزوار')
  }

  if (!components.some(c => c.type === 'testimonial')) {
    suggestions.push('إضافة قسم آراء العملاء يمكن أن يزيد من الثقة')
  }

  if (!components.some(c => c.type === 'gallery')) {
    suggestions.push('معرض الصور يمكن أن يجعل الصفحة أكثر جاذبية')
  }

  suggestions.push('تأكد من تحسين الصفحة للهواتف المحمولة')
  suggestions.push('استخدم ألوان متناسقة مع هوية علامتك التجارية')

  return suggestions
}
