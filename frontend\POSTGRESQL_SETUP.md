# 🐘 دليل إعداد PostgreSQL للمنصة المغربية لأزياء التخرج

## 📋 المتطلبات الأساسية

### 1. تثبيت PostgreSQL
```bash
# على Windows
# تحميل من: https://www.postgresql.org/download/windows/

# على macOS
brew install postgresql

# على Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# على CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
```

### 2. تثبيت Dependencies
```bash
cd frontend
npm install pg bcryptjs
npm install -D @types/pg @types/bcryptjs
```

## 🔧 إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```sql
-- الاتصال بـ PostgreSQL
psql -U postgres

-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE graduation_platform;

-- إنشاء مستخدم جديد (اختياري)
CREATE USER graduation_user WITH PASSWORD 'your_secure_password';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE graduation_platform TO graduation_user;

-- الخروج
\q
```

### 2. إعداد متغيرات البيئة
```bash
# إنشاء ملف .env.local
cp .env.example .env.local
```

```env
# في ملف .env.local
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=graduation_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password_here

NODE_ENV=development
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
```

## 🚀 تشغيل النظام

### 1. تهيئة قاعدة البيانات
```bash
# تشغيل الخادم
npm run dev

# في متصفح آخر أو Postman
POST http://localhost:3000/api/database/init
```

### 2. التحقق من حالة قاعدة البيانات
```bash
GET http://localhost:3000/api/database/init
```

### 3. اختبار APIs الجديدة
```bash
# جلب المنتجات من PostgreSQL
GET http://localhost:3000/api/products/postgres

# جلب المستخدمين
GET http://localhost:3000/api/users

# جلب الطلبات
GET http://localhost:3000/api/orders/postgres

# جلب الإحصائيات
GET http://localhost:3000/api/stats
```

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية:

#### 1. **users** - المستخدمين
```sql
- id (UUID, Primary Key)
- email (VARCHAR, Unique)
- password_hash (VARCHAR)
- first_name (VARCHAR)
- last_name (VARCHAR)
- phone (VARCHAR)
- role (ENUM: admin, customer, school, delivery)
- is_active (BOOLEAN)
- email_verified (BOOLEAN)
- profile_image (TEXT)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 2. **products** - المنتجات
```sql
- id (UUID, Primary Key)
- name (VARCHAR)
- description (TEXT)
- category_id (UUID, Foreign Key)
- price (DECIMAL)
- rental_price (DECIMAL)
- colors (TEXT[])
- sizes (TEXT[])
- images (TEXT[])
- stock_quantity (INTEGER)
- is_available (BOOLEAN)
- is_published (BOOLEAN)
- features (TEXT[])
- specifications (JSONB)
- rating (DECIMAL)
- reviews_count (INTEGER)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 3. **orders** - الطلبات
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- school_id (UUID, Foreign Key)
- order_number (VARCHAR, Unique)
- status (ENUM)
- total_amount (DECIMAL)
- shipping_amount (DECIMAL)
- tax_amount (DECIMAL)
- discount_amount (DECIMAL)
- payment_method (VARCHAR)
- payment_status (ENUM)
- shipping_address (JSONB)
- billing_address (JSONB)
- notes (TEXT)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 4. **order_items** - عناصر الطلب
```sql
- id (UUID, Primary Key)
- order_id (UUID, Foreign Key)
- product_id (UUID, Foreign Key)
- quantity (INTEGER)
- unit_price (DECIMAL)
- total_price (DECIMAL)
- type (ENUM: purchase, rental)
- size (VARCHAR)
- color (VARCHAR)
- customizations (JSONB)
- created_at (TIMESTAMP)
```

## 🔍 الفهارس المحسنة للأداء

```sql
-- فهارس المنتجات
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_published ON products(is_published);
CREATE INDEX idx_products_available ON products(is_available);

-- فهارس الطلبات
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order ON order_items(order_id);

-- فهارس المستخدمين
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
```

## 🎯 الميزات الجديدة

### 1. **أداء محسن**
- استعلامات محسنة مع فهارس
- Connection pooling
- معاملات آمنة (Transactions)

### 2. **أمان متقدم**
- تشفير كلمات المرور بـ bcrypt
- حماية من SQL Injection
- التحقق من صحة البيانات

### 3. **مرونة في البحث**
- بحث متقدم في المنتجات
- فلترة ديناميكية
- ترتيب مخصص

### 4. **إحصائيات شاملة**
- إحصائيات المستخدمين
- إحصائيات المبيعات
- تحليل الأداء

## 🧪 اختبار النظام

### 1. إنشاء منتج جديد
```bash
POST http://localhost:3000/api/products/postgres
Content-Type: application/json

{
  "name": "ثوب التخرج الفاخر",
  "description": "ثوب تخرج عالي الجودة",
  "price": 299.99,
  "rental_price": 99.99,
  "colors": ["أسود", "أزرق"],
  "sizes": ["S", "M", "L"],
  "images": ["/images/gown1.jpg"],
  "stock_quantity": 50,
  "is_available": true,
  "is_published": true,
  "features": ["مقاوم للتجاعيد", "قابل للغسل"]
}
```

### 2. إنشاء مستخدم جديد
```bash
POST http://localhost:3000/api/users
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "first_name": "أحمد",
  "last_name": "محمد",
  "phone": "+************",
  "role": "customer"
}
```

### 3. إنشاء طلب جديد
```bash
POST http://localhost:3000/api/orders/postgres
Content-Type: application/json

{
  "user_id": "user-uuid-here",
  "items": [
    {
      "product_id": "product-uuid-here",
      "quantity": 1,
      "unit_price": 299.99,
      "type": "purchase"
    }
  ],
  "shipping_address": {
    "street": "شارع محمد الخامس",
    "city": "الرباط",
    "country": "المغرب"
  },
  "payment_method": "bank_transfer"
}
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ الاتصال بقاعدة البيانات
```bash
# التحقق من تشغيل PostgreSQL
sudo systemctl status postgresql

# إعادة تشغيل PostgreSQL
sudo systemctl restart postgresql
```

#### 2. خطأ في الصلاحيات
```sql
-- منح جميع الصلاحيات
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO graduation_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO graduation_user;
```

#### 3. خطأ في متغيرات البيئة
```bash
# التحقق من وجود ملف .env.local
ls -la .env.local

# التحقق من المحتوى
cat .env.local
```

## 📈 مراقبة الأداء

### 1. مراقبة الاستعلامات
```sql
-- تفعيل تسجيل الاستعلامات البطيئة
ALTER SYSTEM SET log_min_duration_statement = 1000;
SELECT pg_reload_conf();
```

### 2. مراقبة الاتصالات
```sql
-- عرض الاتصالات النشطة
SELECT * FROM pg_stat_activity;
```

### 3. مراقبة حجم قاعدة البيانات
```sql
-- حجم قاعدة البيانات
SELECT pg_size_pretty(pg_database_size('graduation_platform'));
```

## 🚀 النشر في الإنتاج

### 1. إعداد متغيرات البيئة للإنتاج
```env
NODE_ENV=production
POSTGRES_HOST=your-production-host
POSTGRES_PORT=5432
POSTGRES_DB=graduation_platform_prod
POSTGRES_USER=graduation_user
POSTGRES_PASSWORD=very_secure_password
```

### 2. تحسينات الأمان
```sql
-- إنشاء مستخدم محدود الصلاحيات للإنتاج
CREATE USER app_user WITH PASSWORD 'secure_production_password';
GRANT CONNECT ON DATABASE graduation_platform_prod TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
```

**النظام جاهز الآن للعمل مع PostgreSQL بكفاءة عالية! 🎉**
